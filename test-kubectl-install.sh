#!/bin/bash
# 测试kubectl安装函数的架构检测

set -e

echo "=== 测试kubectl安装函数的架构检测 ==="
echo "时间: $(date)"
echo

# 导入颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 导入LOG_FILE定义
LOG_FILE="/tmp/test-kubectl-$$.log"

# 导入log函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

# 测试架构检测逻辑
test_arch_detection() {
    echo "1. 测试架构检测逻辑..."
    
    # 模拟不同的架构
    local test_archs=("x86_64" "aarch64" "arm64" "unknown")
    
    for test_arch in "${test_archs[@]}"; do
        echo "测试架构: $test_arch"
        
        case "$test_arch" in
            "x86_64")
                local arch="amd64"
                echo "  ✓ $test_arch -> $arch"
                ;;
            "aarch64"|"arm64")
                local arch="arm64"
                echo "  ✓ $test_arch -> $arch"
                ;;
            *)
                echo "  ✗ $test_arch -> 不支持"
                ;;
        esac
    done
}

# 测试当前系统架构
test_current_arch() {
    echo "2. 测试当前系统架构..."
    
    local raw_arch=$(uname -m)
    echo "当前系统架构: $raw_arch"
    
    case "$raw_arch" in
        "x86_64")
            local arch="amd64"
            echo "✓ 映射为: $arch"
            ;;
        "aarch64"|"arm64")
            local arch="arm64"
            echo "✓ 映射为: $arch"
            ;;
        *)
            echo "✗ 不支持的架构: $raw_arch"
            return 1
            ;;
    esac
    
    echo "✓ 架构检测正常"
}

# 测试kubectl下载URL构建
test_kubectl_url() {
    echo "3. 测试kubectl下载URL构建..."
    
    local kubectl_version="v1.28.0"  # 示例版本
    local raw_arch=$(uname -m)
    
    case "$raw_arch" in
        "x86_64")
            local arch="amd64"
            ;;
        "aarch64"|"arm64")
            local arch="arm64"
            ;;
        *)
            echo "✗ 不支持的架构: $raw_arch"
            return 1
            ;;
    esac
    
    local kubectl_url="https://dl.k8s.io/release/${kubectl_version}/bin/linux/${arch}/kubectl"
    local checksum_url="https://dl.k8s.io/${kubectl_version}/bin/linux/${arch}/kubectl.sha256"
    
    echo "kubectl下载URL: $kubectl_url"
    echo "校验和URL: $checksum_url"
    
    # 测试URL是否可访问（仅检查HTTP状态码）
    if curl -s --head "$kubectl_url" | head -n 1 | grep -q "200 OK"; then
        echo "✓ kubectl下载URL可访问"
    else
        echo "⚠ kubectl下载URL可能不可访问（可能是网络问题）"
    fi
}

# 运行所有测试
echo "开始测试..."
echo

test_arch_detection
echo

test_current_arch
echo

test_kubectl_url
echo

echo "=== 测试完成 ==="
echo "日志文件: $LOG_FILE"

# 清理
rm -f "$LOG_FILE"
