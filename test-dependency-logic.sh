#!/bin/bash
# 测试修正后的依赖检查逻辑

set -e

echo "=== 测试依赖检查逻辑修正 ==="
echo "时间: $(date)"
echo

# 导入颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 导入LOG_FILE定义
LOG_FILE="/tmp/test-deps-$$.log"

# 导入log函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

# 测试1: 验证执行顺序
test_execution_order() {
    echo "1. 测试执行顺序..."
    
    echo "✓ 正确的执行顺序应该是："
    echo "  1. 检测系统环境"
    echo "  2. 收集部署配置（包括服务器类型选择）"
    echo "  3. 根据服务器类型检查和安装依赖"
    echo "  4. K3s健康检查（仅内部服务器）"
    echo "  5. 验证配置"
    echo "  6. 执行部署"
    
    # 检查main函数中的步骤顺序
    if grep -A 20 "# 步骤2：检测环境" ../matrix-deployment-package/setup.sh | grep -q "# 步骤3：收集配置"; then
        echo "✓ 步骤2-3顺序正确：先检测环境，后收集配置"
    else
        echo "✗ 步骤2-3顺序错误"
        return 1
    fi
    
    if grep -A 20 "# 步骤3：收集配置" ../matrix-deployment-package/setup.sh | grep -q "# 步骤4：根据服务器类型检查和安装依赖"; then
        echo "✓ 步骤3-4顺序正确：先收集配置，后检查依赖"
    else
        echo "✗ 步骤3-4顺序错误"
        return 1
    fi
}

# 测试2: 验证依赖检查逻辑
test_dependency_logic() {
    echo "2. 测试依赖检查逻辑..."
    
    # 检查是否有SERVER_TYPE检查
    if grep -q "if \[\[ \"\$SERVER_TYPE\" == \"internal\" \]\]" ../matrix-deployment-package/setup.sh; then
        echo "✓ 依赖检查包含内部服务器类型判断"
    else
        echo "✗ 缺少内部服务器类型判断"
        return 1
    fi
    
    if grep -q "elif \[\[ \"\$SERVER_TYPE\" == \"external\" \]\]" ../matrix-deployment-package/setup.sh; then
        echo "✓ 依赖检查包含外部服务器类型判断"
    else
        echo "✗ 缺少外部服务器类型判断"
        return 1
    fi
    
    # 检查内部服务器依赖
    local internal_deps=("Docker" "kubectl" "Helm" "K3s" "Python3")
    for dep in "${internal_deps[@]}"; do
        if grep -A 50 "if \[\[ \"\$SERVER_TYPE\" == \"internal\" \]\]" ../matrix-deployment-package/setup.sh | grep -q "$dep"; then
            echo "✓ 内部服务器依赖包含: $dep"
        else
            echo "⚠ 内部服务器依赖可能缺少: $dep"
        fi
    done
    
    # 检查外部服务器依赖
    local external_deps=("Nginx" "Certbot")
    for dep in "${external_deps[@]}"; do
        if grep -A 20 "elif \[\[ \"\$SERVER_TYPE\" == \"external\" \]\]" ../matrix-deployment-package/setup.sh | grep -q "$dep"; then
            echo "✓ 外部服务器依赖包含: $dep"
        else
            echo "⚠ 外部服务器依赖可能缺少: $dep"
        fi
    done
}

# 测试3: 验证安装函数
test_install_functions() {
    echo "3. 测试安装函数..."
    
    local required_functions=("install_basic_tools" "install_python3" "install_nginx" "install_certbot")
    for func in "${required_functions[@]}"; do
        if grep -q "^${func}()" ../matrix-deployment-package/setup.sh; then
            echo "✓ 安装函数存在: $func"
        else
            echo "✗ 安装函数缺失: $func"
            return 1
        fi
    done
}

# 测试4: 验证去重逻辑
test_deduplication() {
    echo "4. 测试去重逻辑..."
    
    if grep -q "unique_functions" ../matrix-deployment-package/setup.sh; then
        echo "✓ 包含去重逻辑"
    else
        echo "✗ 缺少去重逻辑"
        return 1
    fi
    
    if grep -q "for existing_func in" ../matrix-deployment-package/setup.sh; then
        echo "✓ 包含重复检查逻辑"
    else
        echo "✗ 缺少重复检查逻辑"
        return 1
    fi
}

# 运行所有测试
echo "开始测试..."
echo

test_execution_order
echo

test_dependency_logic
echo

test_install_functions
echo

test_deduplication
echo

echo "=== 测试完成 ==="
echo "日志文件: $LOG_FILE"

# 清理
rm -f "$LOG_FILE"

echo
echo "✅ 依赖检查逻辑修正验证完成！"
echo
echo "主要改进："
echo "1. ✅ 执行顺序修正：先收集配置，后检查依赖"
echo "2. ✅ 服务器类型区分：内部服务器 vs 外部服务器"
echo "3. ✅ 依赖分类：基础工具 + 特定依赖"
echo "4. ✅ 新增安装函数：Python3、Nginx、Certbot等"
echo "5. ✅ 去重逻辑：避免重复安装相同依赖"
