#!/bin/bash
# 上传修正后的部署包到测试服务器

set -e

# 服务器配置
SERVER_IP="**********"
SERVER_USER="jw"
SERVER_PASS="test123"
REMOTE_DIR="/home/<USER>/matrix-deployment"

echo "=== 上传Matrix部署包到测试服务器 ==="
echo "服务器: $SERVER_IP"
echo "用户: $SERVER_USER"
echo "目标目录: $REMOTE_DIR"
echo

# 检查sshpass是否安装
if ! command -v sshpass >/dev/null 2>&1; then
    echo "安装sshpass..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install sshpass 2>/dev/null || {
            echo "请手动安装sshpass: brew install sshpass"
            exit 1
        }
    else
        sudo apt-get update && sudo apt-get install -y sshpass
    fi
fi

# 创建临时压缩包
echo "创建部署包压缩文件..."
cd matrix-deployment-package
tar -czf ../matrix-deployment-package.tar.gz .
cd ..

# 上传文件到服务器
echo "上传文件到服务器..."
sshpass -p "$SERVER_PASS" scp matrix-deployment-package.tar.gz "$SERVER_USER@$SERVER_IP:/tmp/"

# 在服务器上解压和设置
echo "在服务器上解压和设置..."
sshpass -p "$SERVER_PASS" ssh "$SERVER_USER@$SERVER_IP" << 'EOF'
# 清理旧文件
rm -rf /home/<USER>/matrix-deployment

# 创建目录并解压
mkdir -p /home/<USER>/matrix-deployment
cd /home/<USER>/matrix-deployment
tar -xzf /tmp/matrix-deployment-package.tar.gz

# 设置执行权限
chmod +x setup.sh
chmod +x scripts/*.sh
chmod +x scripts/*.py

# 显示文件列表
echo "部署包文件列表："
ls -la

echo "✓ 部署包已成功上传到服务器"
echo "✓ 文件位置: /home/<USER>/matrix-deployment/"
echo
echo "现在可以在服务器上运行："
echo "cd /home/<USER>/matrix-deployment && ./setup.sh"
EOF

# 清理临时文件
rm -f matrix-deployment-package.tar.gz

echo
echo "=== 上传完成 ==="
echo "请登录服务器继续测试："
echo "ssh jw@**********"
echo "cd /home/<USER>/matrix-deployment"
echo "./setup.sh"
