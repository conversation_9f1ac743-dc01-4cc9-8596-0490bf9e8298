#!/usr/bin/expect -f
# Matrix部署服务器测试脚本

set timeout 1800
set server "**********"
set username "jw"
set password "test123"

# 记录开始时间
set start_time [clock seconds]

puts "=== Matrix部署服务器测试 ==="
puts "服务器: $server"
puts "用户: $username"
puts "开始时间: [clock format $start_time]"
puts ""

# 连接到服务器
spawn ssh -o StrictHostKeyChecking=no $username@$server

# 等待密码提示
expect {
    "password:" {
        send "$password\r"
    }
    "Password:" {
        send "$password\r"
    }
    timeout {
        puts "连接超时"
        exit 1
    }
}

# 等待登录成功
expect {
    "$ " {
        puts "✓ 成功登录到服务器"
    }
    "# " {
        puts "✓ 成功登录到服务器（root权限）"
    }
    timeout {
        puts "登录失败"
        exit 1
    }
}

# 收集系统信息
send "echo '=== 系统信息 ==='\r"
expect "$ "

send "cat /etc/os-release | grep PRETTY_NAME\r"
expect "$ "

send "uname -a\r"
expect "$ "

send "whoami\r"
expect "$ "

send "pwd\r"
expect "$ "

send "date\r"
expect "$ "

# 检查现有服务状态
send "echo '=== 现有服务状态 ==='\r"
expect "$ "

send "systemctl status k3s 2>/dev/null || echo 'K3s未安装'\r"
expect "$ "

send "command -v kubectl && echo 'kubectl已安装' || echo 'kubectl未安装'\r"
expect "$ "

send "command -v helm && echo 'Helm已安装' || echo 'Helm未安装'\r"
expect "$ "

send "command -v docker && echo 'Docker已安装' || echo 'Docker未安装'\r"
expect "$ "

# 测试网络连接
send "echo '=== 网络连接测试 ==='\r"
expect "$ "

send "curl -I https://raw.githubusercontent.com/niublab/test/main/setup.sh\r"
expect "$ "

# 开始部署测试
send "echo '=== 开始Matrix部署测试 ==='\r"
expect "$ "

send "echo '部署开始时间: '\$(date)\r"
expect "$ "

send "echo '执行命令: bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)'\r"
expect "$ "

send "echo '----------------------------------------'\r"
expect "$ "

# 执行部署脚本
send "bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)\r"

# 等待部署完成，设置较长的超时时间
set timeout 1800

# 监控部署过程
expect {
    "请选择服务器类型" {
        puts "检测到服务器类型选择提示"
        send "1\r"
        exp_continue
    }
    "请输入主域名" {
        puts "检测到域名输入提示"
        send "matrix-test.local\r"
        exp_continue
    }
    "是否自动安装缺失的依赖" {
        puts "检测到依赖安装确认"
        send "y\r"
        exp_continue
    }
    "部署完成" {
        puts "✓ 检测到部署完成信息"
        break
    }
    "部署成功" {
        puts "✓ 检测到部署成功信息"
        break
    }
    "ERROR" {
        puts "⚠ 检测到错误信息"
        exp_continue
    }
    "错误" {
        puts "⚠ 检测到错误信息"
        exp_continue
    }
    timeout {
        puts "部署超时（30分钟）"
        break
    }
    eof {
        puts "连接意外断开"
        break
    }
}

# 等待命令提示符
expect "$ "

# 记录结束时间
set end_time [clock seconds]
set duration [expr $end_time - $start_time]
set minutes [expr $duration / 60]
set seconds [expr $duration % 60]

send "echo '----------------------------------------'\r"
expect "$ "

send "echo '部署完成时间: '\$(date)\r"
expect "$ "

send "echo '总耗时: ${minutes}分${seconds}秒'\r"
expect "$ "

# 验证部署结果
send "echo '=== 部署结果验证 ==='\r"
expect "$ "

# 检查Kubernetes集群
send "echo '1. 检查Kubernetes集群状态:'\r"
expect "$ "

send "command -v kubectl >/dev/null 2>&1 && kubectl cluster-info || echo 'kubectl不可用或集群连接失败'\r"
expect "$ "

# 检查K3s状态
send "echo '2. 检查K3s服务状态:'\r"
expect "$ "

send "systemctl is-active --quiet k3s 2>/dev/null && echo '✓ K3s服务运行正常' || echo '✗ K3s服务未运行'\r"
expect "$ "

# 检查Matrix命名空间
send "echo '3. 检查Matrix命名空间和资源:'\r"
expect "$ "

send "kubectl get namespace matrix >/dev/null 2>&1 && echo '✓ Matrix命名空间存在' || echo '✗ Matrix命名空间不存在'\r"
expect "$ "

send "kubectl get pods -n matrix 2>/dev/null || echo '无法获取Pod信息'\r"
expect "$ "

send "kubectl get services -n matrix 2>/dev/null || echo '无法获取Service信息'\r"
expect "$ "

# 检查配置文件
send "echo '4. 检查配置文件:'\r"
expect "$ "

send "ls -la ~/matrix/ 2>/dev/null && echo '✓ Matrix配置目录存在' || echo '✗ Matrix配置目录不存在'\r"
expect "$ "

# 生成测试总结
send "echo '=== 测试总结 ==='\r"
expect "$ "

send "echo '部署耗时: ${minutes}分${seconds}秒'\r"
expect "$ "

if {$duration < 60} {
    send "echo '⚠️  警告: 部署时间过短，可能没有执行真正的部署'\r"
} elseif {$duration >= 300 && $duration <= 900} {
    send "echo '✓ 部署时间正常 (5-15分钟范围内)'\r"
} else {
    send "echo 'ℹ️  部署时间: ${minutes}分${seconds}秒'\r"
}
expect "$ "

send "echo '测试完成时间: '\$(date)\r"
expect "$ "

puts ""
puts "=== 测试脚本执行完成 ==="
puts "总耗时: ${minutes}分${seconds}秒"

if {$duration < 60} {
    puts "⚠️  警告: 部署时间异常短，请检查部署是否真正执行"
} elseif {$duration >= 300 && $duration <= 900} {
    puts "✓ 部署时间在正常范围内"
} else {
    puts "ℹ️  部署时间: ${minutes}分${seconds}秒"
}

# 保持连接以便查看结果
send "echo '按Ctrl+C退出或继续查看结果'\r"
expect "$ "

interact
