#!/usr/bin/expect -f
# Matrix内部服务器部署测试脚本
# 服务器: **********
# 域名: 031908.xyz

set timeout 1800
set server "**********"
set username "jw"
set password "test123"

# 配置信息
set domain "031908.xyz"
set cloudflare_token "****************************************"
set email "<EMAIL>"

# 记录开始时间
set start_time [clock seconds]

puts "=== Matrix内部服务器部署测试 ==="
puts "服务器: $server"
puts "域名: $domain"
puts "开始时间: [clock format $start_time]"
puts ""

# 连接到服务器
spawn ssh -o StrictHostKeyChecking=no $username@$server

# 等待密码提示
expect {
    "password:" {
        send "$password\r"
    }
    "Password:" {
        send "$password\r"
    }
    timeout {
        puts "连接超时"
        exit 1
    }
}

# 等待登录成功
expect {
    "$ " {
        puts "✓ 成功登录到Debian服务器"
    }
    "# " {
        puts "✓ 成功登录到Debian服务器（root权限）"
    }
    timeout {
        puts "登录失败"
        exit 1
    }
}

# 收集系统信息
send "echo '=== 系统环境信息 ==='\r"
expect "$ "

send "cat /etc/os-release | grep PRETTY_NAME\r"
expect "$ "

send "uname -a\r"
expect "$ "

send "free -h\r"
expect "$ "

send "df -h /\r"
expect "$ "

# 清理之前的部署（如果存在）
send "echo '=== 清理之前的部署 ==='\r"
expect "$ "

send "sudo kubectl delete namespace matrix 2>/dev/null || echo '无需清理Matrix命名空间'\r"
expect "$ "

send "sudo systemctl stop matrix-virtual-ip.service 2>/dev/null || echo '无需停止虚拟IP服务'\r"
expect "$ "

send "rm -rf ~/matrix/ 2>/dev/null || echo '无需清理配置目录'\r"
expect "$ "

# 开始部署
send "echo '=== 开始Matrix内部服务器部署 ==='\r"
expect "$ "

send "echo '部署开始时间: '\$(date)\r"
expect "$ "

send "echo '执行命令: bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)'\r"
expect "$ "

# 执行部署脚本
send "bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)\r"

# 处理部署过程中的交互
expect {
    "请选择服务器类型" {
        puts "✓ 检测到服务器类型选择"
        send "1\r"
        exp_continue
    }
    "内部服务器" {
        puts "✓ 选择内部服务器"
        exp_continue
    }
    "请输入主域名" {
        puts "✓ 输入主域名: $domain"
        send "$domain\r"
        exp_continue
    }
    "请输入HTTPS端口" {
        puts "✓ 使用默认HTTPS端口"
        send "\r"
        exp_continue
    }
    "请输入联邦端口" {
        puts "✓ 使用默认联邦端口"
        send "\r"
        exp_continue
    }
    "请输入RouterOS路由器IP" {
        puts "✓ 输入RouterOS IP"
        send "***********\r"
        exp_continue
    }
    "请输入RouterOS API端口" {
        puts "✓ 使用默认API端口"
        send "\r"
        exp_continue
    }
    "请输入RouterOS用户名" {
        puts "✓ 输入RouterOS用户名"
        send "admin\r"
        exp_continue
    }
    "请输入RouterOS密码" {
        puts "✓ 输入RouterOS密码"
        send "admin123\r"
        exp_continue
    }
    "请输入WAN接口名称" {
        puts "✓ 使用默认WAN接口"
        send "\r"
        exp_continue
    }
    "请输入Cloudflare API Token" {
        puts "✓ 输入Cloudflare API Token"
        send "$cloudflare_token\r"
        exp_continue
    }
    "请输入注册邮箱" {
        puts "✓ 输入注册邮箱: $email"
        send "$email\r"
        exp_continue
    }
    "是否自动安装缺失的依赖" {
        puts "✓ 确认自动安装依赖"
        send "y\r"
        exp_continue
    }
    "检测到缺失的依赖" {
        puts "✓ 检测到依赖检查"
        exp_continue
    }
    "安装.*Docker" {
        puts "✓ 正在安装Docker"
        exp_continue
    }
    "安装.*kubectl" {
        puts "✓ 正在安装kubectl"
        exp_continue
    }
    "安装.*Helm" {
        puts "✓ 正在安装Helm"
        exp_continue
    }
    "安装.*K3s" {
        puts "✓ 正在安装K3s"
        exp_continue
    }
    "步骤.*生成Helm配置" {
        puts "✓ 生成Helm配置文件"
        exp_continue
    }
    "步骤.*下载管理脚本" {
        puts "✓ 下载管理脚本"
        exp_continue
    }
    "步骤.*部署Matrix服务栈" {
        puts "✓ 部署Matrix服务栈"
        exp_continue
    }
    "等待.*启动" {
        puts "✓ 等待服务启动"
        exp_continue
    }
    "部署完成" {
        puts "✓ 检测到部署完成"
        break
    }
    "部署成功" {
        puts "✓ 检测到部署成功"
        break
    }
    "所有验证项目通过" {
        puts "✓ 所有验证通过"
        break
    }
    "ERROR" {
        puts "⚠ 检测到错误信息"
        exp_continue
    }
    "错误" {
        puts "⚠ 检测到错误信息"
        exp_continue
    }
    timeout {
        puts "部署超时（30分钟）"
        break
    }
    eof {
        puts "连接意外断开"
        break
    }
}

# 等待命令提示符
expect "$ "

# 记录结束时间
set end_time [clock seconds]
set duration [expr $end_time - $start_time]
set minutes [expr $duration / 60]
set seconds [expr $duration % 60]

send "echo '=== 部署完成，开始验证 ==='\r"
expect "$ "

send "echo '部署耗时: ${minutes}分${seconds}秒'\r"
expect "$ "

# 详细验证部署结果
send "echo '=== 详细验证部署结果 ==='\r"
expect "$ "

# 1. 验证Kubernetes集群
send "echo '1. Kubernetes集群状态:'\r"
expect "$ "

send "kubectl cluster-info\r"
expect "$ "

send "kubectl get nodes -o wide\r"
expect "$ "

# 2. 验证Matrix命名空间和资源
send "echo '2. Matrix命名空间资源:'\r"
expect "$ "

send "kubectl get namespace matrix\r"
expect "$ "

send "kubectl get all -n matrix\r"
expect "$ "

send "kubectl get pods -n matrix -o wide\r"
expect "$ "

# 3. 验证服务状态
send "echo '3. 服务状态详情:'\r"
expect "$ "

send "kubectl describe pods -n matrix | grep -E '(Name:|Status:|Ready:)'\r"
expect "$ "

# 4. 验证配置文件
send "echo '4. 配置文件检查:'\r"
expect "$ "

send "ls -la ~/matrix/config/\r"
expect "$ "

send "cat ~/matrix/deployment-report.txt 2>/dev/null || echo '部署报告不存在'\r"
expect "$ "

# 5. 验证系统服务
send "echo '5. 系统服务状态:'\r"
expect "$ "

send "systemctl status k3s --no-pager\r"
expect "$ "

send "systemctl status matrix-virtual-ip.service --no-pager 2>/dev/null || echo '虚拟IP服务未配置'\r"
expect "$ "

# 6. 验证网络连通性
send "echo '6. 网络连通性测试:'\r"
expect "$ "

send "kubectl exec -n matrix deployment/synapse -- curl -f http://localhost:8008/_matrix/client/versions 2>/dev/null && echo '✓ Synapse API可访问' || echo '✗ Synapse API不可访问'\r"
expect "$ "

# 7. 验证端口配置
send "echo '7. 端口配置检查:'\r"
expect "$ "

send "kubectl get services -n matrix -o wide\r"
expect "$ "

send "sudo netstat -tlnp | grep -E ':(8443|8448|30)' || echo '未发现配置的端口'\r"
expect "$ "

# 生成最终测试报告
send "echo '=== 最终测试报告 ==='\r"
expect "$ "

send "echo '部署信息:'\r"
expect "$ "
send "echo '- 服务器: $server'\r"
expect "$ "
send "echo '- 域名: $domain'\r"
expect "$ "
send "echo '- 部署耗时: ${minutes}分${seconds}秒'\r"
expect "$ "

if {$duration < 60} {
    send "echo '⚠️  警告: 部署时间过短，可能没有执行真正的部署'\r"
} elseif {$duration >= 300 && $duration <= 900} {
    send "echo '✓ 部署时间正常 (5-15分钟范围内)'\r"
} else {
    send "echo 'ℹ️  部署时间: ${minutes}分${seconds}秒'\r"
}
expect "$ "

send "echo '验证结果:'\r"
expect "$ "
send "kubectl get namespace matrix >/dev/null 2>&1 && echo '✓ Matrix命名空间存在' || echo '✗ Matrix命名空间不存在'\r"
expect "$ "
send "kubectl get pods -n matrix --no-headers 2>/dev/null | wc -l | xargs echo '- Pod数量:'\r"
expect "$ "
send "kubectl get services -n matrix --no-headers 2>/dev/null | wc -l | xargs echo '- Service数量:'\r"
expect "$ "

send "echo '配置的访问地址:'\r"
expect "$ "
send "echo '- 主页: https://$domain:8443'\r"
expect "$ "
send "echo '- Element Web: https://chat.$domain:8443'\r"
expect "$ "
send "echo '- Matrix服务器: https://matrix.$domain:8443'\r"
expect "$ "
send "echo '- 账户管理: https://mas.$domain:8443'\r"
expect "$ "
send "echo '- 音视频通话: https://rtc.$domain:8443'\r"
expect "$ "

send "echo '测试完成时间: '\$(date)\r"
expect "$ "

puts ""
puts "=== Matrix内部服务器部署测试完成 ==="
puts "总耗时: ${minutes}分${seconds}秒"
puts "域名: $domain"

if {$duration < 60} {
    puts "⚠️  警告: 部署时间异常短，请检查部署是否真正执行"
} elseif {$duration >= 300 && $duration <= 900} {
    puts "✓ 部署时间在正常范围内，可能成功部署"
} else {
    puts "ℹ️  部署时间: ${minutes}分${seconds}秒"
}

puts "请检查上述验证结果以确认部署状态"

# 保持连接以便查看结果
send "echo '按Ctrl+C退出或继续操作'\r"
expect "$ "

interact
