# Matrix部署服务器测试指南

## 测试环境
- **服务器**: **********
- **操作系统**: Debian Linux
- **用户**: jw
- **密码**: test123

## 第一步：登录测试服务器

```bash
ssh jw@**********
# 输入密码: test123
```

## 第二步：环境准备和信息收集

登录后，首先收集系统信息：

```bash
# 显示系统信息
echo "=== 系统信息 ==="
cat /etc/os-release
uname -a
whoami
pwd
date

# 检查现有服务状态
echo "=== 现有服务状态 ==="
systemctl status k3s 2>/dev/null || echo "K3s未安装"
command -v kubectl && echo "kubectl已安装" || echo "kubectl未安装"
command -v helm && echo "Helm已安装" || echo "Helm未安装"
command -v docker && echo "Docker已安装" || echo "Docker未安装"

# 检查网络连接
echo "=== 网络连接测试 ==="
curl -I https://raw.githubusercontent.com/niublab/test/main/setup.sh
```

## 第三步：创建测试监控脚本

在服务器上创建一个监控脚本来记录部署过程：

```bash
cat > deployment_monitor.sh << 'EOF'
#!/bin/bash
# 部署监控脚本

echo "=== Matrix部署测试开始 ==="
echo "开始时间: $(date)"
echo "服务器: $(hostname)"
echo "用户: $(whoami)"
echo

# 记录开始时间
START_TIME=$(date +%s)

# 创建日志文件
LOG_FILE="matrix-deployment-$(date +%Y%m%d-%H%M%S).log"
echo "日志文件: $LOG_FILE"

# 执行部署并记录日志
echo "执行部署命令..."
echo "bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)"
echo "----------------------------------------"

# 使用script命令记录完整的交互过程
script -c "bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)" "$LOG_FILE"

# 记录结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
MINUTES=$((DURATION / 60))
SECONDS=$((DURATION % 60))

echo "----------------------------------------"
echo "部署完成时间: $(date)"
echo "总耗时: ${MINUTES}分${SECONDS}秒"

# 验证部署结果
echo
echo "=== 部署结果验证 ==="

# 1. 检查Kubernetes集群
echo "1. Kubernetes集群状态:"
if command -v kubectl >/dev/null 2>&1; then
    if kubectl cluster-info >/dev/null 2>&1; then
        echo "✓ 集群连接正常"
        kubectl get nodes
    else
        echo "✗ 集群连接失败"
    fi
else
    echo "✗ kubectl未安装"
fi

# 2. 检查Matrix命名空间
echo
echo "2. Matrix命名空间资源:"
if kubectl get namespace matrix >/dev/null 2>&1; then
    echo "✓ Matrix命名空间存在"
    echo "Pods:"
    kubectl get pods -n matrix -o wide
    echo "Services:"
    kubectl get services -n matrix
    echo "Deployments:"
    kubectl get deployments -n matrix
else
    echo "✗ Matrix命名空间不存在"
fi

# 3. 检查配置文件
echo
echo "3. 配置文件检查:"
if [[ -d ~/matrix ]]; then
    echo "✓ 配置目录存在: ~/matrix"
    find ~/matrix -type f | head -10
else
    echo "✗ 配置目录不存在"
fi

# 4. 检查服务状态
echo
echo "4. 系统服务状态:"
systemctl status k3s --no-pager || echo "K3s服务异常"

# 生成测试报告
cat > deployment-test-report.txt << REPORT_EOF
Matrix部署测试报告
==================

测试信息:
- 服务器: $(hostname) (**********)
- 操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
- 测试时间: $(date)
- 部署耗时: ${MINUTES}分${SECONDS}秒

部署结果验证:
- Kubernetes集群: $(kubectl cluster-info >/dev/null 2>&1 && echo "正常" || echo "异常")
- Matrix命名空间: $(kubectl get namespace matrix >/dev/null 2>&1 && echo "存在" || echo "不存在")
- Pod数量: $(kubectl get pods -n matrix --no-headers 2>/dev/null | wc -l)
- Service数量: $(kubectl get services -n matrix --no-headers 2>/dev/null | wc -l)
- 配置目录: $([[ -d ~/matrix ]] && echo "存在" || echo "不存在")

时间分析:
$(if [[ $DURATION -lt 60 ]]; then
    echo "⚠️  警告: 部署时间过短($DURATION秒)，可能未执行真正部署"
elif [[ $DURATION -ge 300 && $DURATION -le 900 ]]; then
    echo "✓ 部署时间正常(5-15分钟范围)"
else
    echo "ℹ️  部署时间: ${MINUTES}分${SECONDS}秒"
fi)

详细日志文件: $LOG_FILE
REPORT_EOF

echo
echo "=== 测试完成 ==="
echo "测试报告: deployment-test-report.txt"
echo "详细日志: $LOG_FILE"
echo "总耗时: ${MINUTES}分${SECONDS}秒"

if [[ $DURATION -lt 60 ]]; then
    echo
    echo "⚠️  警告: 部署时间异常短，请检查是否真正执行了部署"
    echo "预期部署时间应为5-15分钟"
fi
EOF

chmod +x deployment_monitor.sh
```

## 第四步：执行部署测试

```bash
# 执行监控脚本
./deployment_monitor.sh
```

## 第五步：详细验证

部署完成后，执行详细验证：

```bash
# 查看测试报告
cat deployment-test-report.txt

# 查看详细日志
less matrix-deployment-*.log

# 手动验证关键组件
echo "=== 手动验证 ==="

# 检查URL构建是否正确
echo "1. 检查日志中的URL格式:"
grep -i "下载URL\|base.*URL\|修正.*URL" matrix-deployment-*.log

# 检查依赖安装过程
echo "2. 检查依赖安装:"
grep -i "服务器类型\|检查.*依赖\|安装.*依赖" matrix-deployment-*.log

# 检查部署步骤
echo "3. 检查部署步骤:"
grep -i "步骤.*/" matrix-deployment-*.log

# 检查Kubernetes资源创建
echo "4. 检查资源创建:"
grep -i "创建\|部署\|等待.*启动" matrix-deployment-*.log

# 检查最终验证
echo "5. 检查最终验证:"
grep -i "验证\|测试.*通过\|部署.*成功" matrix-deployment-*.log
```

## 第六步：问题排查

如果遇到问题，收集以下信息：

```bash
# 收集错误信息
echo "=== 错误信息收集 ==="

# 查看错误日志
grep -i "error\|错误\|失败\|异常" matrix-deployment-*.log

# 检查系统资源
echo "系统资源:"
free -h
df -h
ps aux | grep -E "(k3s|kubectl|helm|docker)" | head -10

# 检查网络连接
echo "网络连接:"
curl -I https://raw.githubusercontent.com/niublab/test/main/setup.sh
ping -c 3 8.8.8.8

# 检查防火墙
echo "防火墙状态:"
sudo ufw status 2>/dev/null || echo "UFW未启用"
```

## 预期结果

**正常部署应该显示：**
1. 部署时间：5-15分钟
2. URL格式：使用/refs/heads/main/格式
3. 依赖检查：根据服务器类型（内部/外部）执行
4. Kubernetes资源：创建Matrix命名空间、Pod、Service
5. 最终验证：所有验证项目通过

**异常情况：**
1. 部署时间过短（<1分钟）：可能没有真正部署
2. URL 404错误：URL构建问题
3. 依赖安装失败：权限或网络问题
4. Kubernetes资源缺失：部署流程问题

## 测试完成后

请提供以下文件内容：
1. `deployment-test-report.txt` - 测试报告
2. `matrix-deployment-*.log` - 详细日志（关键部分）
3. `kubectl get all -n matrix` - Kubernetes资源状态
4. 任何错误信息或异常情况

这将帮助验证所有修复是否在真实环境中有效工作。
