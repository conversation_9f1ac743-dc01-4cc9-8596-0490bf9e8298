# 服务器端测试命令

## 1. 登录测试服务器
```bash
ssh jw@**********
# 密码: test123
```

## 2. 下载并修复部署脚本
```bash
# 下载最新的setup.sh
curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh -o setup.sh

# 创建热修复脚本
cat > hotfix.sh << 'EOF'
#!/bin/bash
# Matrix部署脚本热修复

set -e

SCRIPT_FILE="setup.sh"
BACKUP_FILE="setup.sh.backup.$(date +%s)"

echo "=== Matrix部署脚本热修复 ==="

# 备份原文件
cp "$SCRIPT_FILE" "$BACKUP_FILE"
echo "✓ 已备份原文件: $BACKUP_FILE"

# 修复1: 替换未定义的log_success调用
echo "修复1: 替换log_success调用..."
sed -i 's/log_success "✓/echo "[SUCCESS] ✓/g' "$SCRIPT_FILE"

# 修复2: 修正OS_TYPE变量问题 - 在install_docker函数开始处添加os_type检测
echo "修复2: 修正OS_TYPE变量..."
sed -i '/install_docker() {/,/case.*in/ {
    /log_info "安装Docker..."/a\
    \
    # 检测操作系统类型\
    local os_type=""\
    if [[ -f /etc/os-release ]]; then\
        source /etc/os-release\
        os_type="$ID"\
    elif [[ -f /etc/debian_version ]]; then\
        os_type="debian"\
    elif [[ -f /etc/redhat-release ]]; then\
        os_type="centos"\
    else\
        os_type="unknown"\
    fi
}' "$SCRIPT_FILE"

# 修复3: 替换case语句中的OS_TYPE
echo "修复3: 替换case语句..."
sed -i 's/case "$OS_TYPE" in/case "$os_type" in/g' "$SCRIPT_FILE"
sed -i 's/\$OS_TYPE/\$os_type/g' "$SCRIPT_FILE"

# 修复4: 修正systemctl检查
echo "修复4: 修正systemctl检查..."
sed -i 's/systemctl is-active --quiet k3s/systemctl is-active --quiet k3s 2>\/dev\/null/g' "$SCRIPT_FILE"

# 验证修复结果
echo "验证修复结果..."
if bash -n "$SCRIPT_FILE"; then
    echo "✓ 脚本语法检查通过"
    echo "✓ 热修复完成"
else
    echo "✗ 脚本语法检查失败，恢复备份"
    cp "$BACKUP_FILE" "$SCRIPT_FILE"
    exit 1
fi
EOF

chmod +x hotfix.sh
./hotfix.sh
```

## 3. 测试修复后的脚本
```bash
# 语法检查
bash -n setup.sh

# 如果语法检查通过，运行脚本
chmod +x setup.sh
./setup.sh
```

## 4. 如果还有问题，手动修复
```bash
# 编辑setup.sh文件
nano setup.sh

# 查找并替换以下内容：
# 1. 将所有 log_success "✓ 替换为 echo "[SUCCESS] ✓
# 2. 在install_docker函数中添加os_type变量定义
# 3. 将 $OS_TYPE 替换为 $os_type
```

## 5. 一键部署测试（修复后）
```bash
# 测试自包含模式
SILENT_MODE=false ./setup.sh

# 或者测试远程下载模式
bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)
```

## 6. 如果需要重新上传修复后的文件
在本地运行：
```bash
# 上传修复后的文件到GitHub
git add matrix-deployment-package/setup.sh
git commit -m "修复setup.sh中的变量和函数定义问题"
git push origin main
```

## 故障排除

### 问题1: log_success函数未定义
**解决方案**: 将所有`log_success`调用替换为`echo "[SUCCESS]"`

### 问题2: OS_TYPE变量未定义  
**解决方案**: 在install_docker函数中添加本地os_type变量检测

### 问题3: systemctl命令在某些系统上不可用
**解决方案**: 添加错误重定向 `2>/dev/null`

### 问题4: 权限问题
**解决方案**: 确保脚本有执行权限 `chmod +x setup.sh`
