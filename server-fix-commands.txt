# 服务器端立即修复命令
# 请在服务器上逐行执行以下命令

# 1. 下载setup.sh
curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh -o setup.sh

# 2. 快速修复关键问题
sed -i 's/log_success "✓/echo "[SUCCESS] ✓/g' setup.sh
sed -i 's/log_success(/echo "[SUCCESS]" #log_success(/g' setup.sh
sed -i 's/\$OS_TYPE/\$os_type/g' setup.sh
sed -i 's/case "$OS_TYPE" in/case "$os_type" in/g' setup.sh

# 3. 在install_docker函数开头添加os_type检测
sed -i '/install_docker() {/,/log_info "安装Docker..."/ {
    /log_info "安装Docker..."/a\
    \
    # 检测操作系统类型\
    local os_type=""\
    if [[ -f /etc/os-release ]]; then\
        source /etc/os-release\
        os_type="$ID"\
    elif [[ -f /etc/debian_version ]]; then\
        os_type="debian"\
    elif [[ -f /etc/redhat-release ]]; then\
        os_type="centos"\
    else\
        os_type="unknown"\
    fi
}' setup.sh

# 4. 添加简化的log函数到脚本开头
sed -i '10i\
log_success() { echo -e "\\033[0;32m[SUCCESS]\\033[0m $*"; }\
log_warn() { echo -e "\\033[1;33m[WARN]\\033[0m $*"; }\
LOG_FILE="/tmp/matrix-deployment-$$.log"' setup.sh

# 5. 设置执行权限
chmod +x setup.sh

# 6. 验证语法
bash -n setup.sh

# 7. 如果语法检查通过，执行脚本
./setup.sh

# 如果还有问题，可以使用一键修复脚本：
# curl -fsSL https://raw.githubusercontent.com/niublab/test/main/quick-deploy.sh | bash
