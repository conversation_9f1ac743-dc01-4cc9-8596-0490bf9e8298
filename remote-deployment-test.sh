#!/bin/bash
# 远程部署测试脚本 - 在Debian服务器上执行Matrix部署

echo "=== Matrix部署脚本远程测试 ==="
echo "目标服务器: ********** (Debian)"
echo "测试时间: $(date)"
echo "仓库地址: https://github.com/niublab/test/main"
echo

# 创建远程执行脚本
cat > server_test_script.sh << 'EOF'
#!/bin/bash
# 在Debian服务器上执行的测试脚本

set -e

echo "=== 在Debian服务器上开始Matrix部署测试 ==="
echo "服务器信息:"
echo "- 操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "- 内核版本: $(uname -r)"
echo "- 架构: $(uname -m)"
echo "- 当前用户: $(whoami)"
echo "- 当前目录: $(pwd)"
echo "- 测试开始时间: $(date)"
echo

# 清理之前的测试文件
echo "清理之前的测试文件..."
rm -f setup.sh matrix-deployment-*.log
rm -rf ~/matrix/ 2>/dev/null || true

# 记录部署开始时间
START_TIME=$(date +%s)
echo "部署开始时间: $(date)"

# 执行部署脚本
echo "执行Matrix部署脚本..."
echo "命令: bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)"
echo "----------------------------------------"

# 使用tee记录所有输出
bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh) 2>&1 | tee matrix-deployment-test.log

# 记录部署结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
MINUTES=$((DURATION / 60))
SECONDS=$((DURATION % 60))

echo "----------------------------------------"
echo "部署完成时间: $(date)"
echo "总部署耗时: ${MINUTES}分${SECONDS}秒"

# 验证部署结果
echo
echo "=== 部署结果验证 ==="

# 检查Kubernetes集群状态
echo "1. 检查Kubernetes集群状态:"
if command -v kubectl >/dev/null 2>&1; then
    echo "kubectl已安装"
    if kubectl cluster-info >/dev/null 2>&1; then
        echo "✓ Kubernetes集群连接正常"
        kubectl get nodes
    else
        echo "✗ Kubernetes集群连接失败"
    fi
else
    echo "✗ kubectl未安装"
fi

# 检查K3s状态
echo
echo "2. 检查K3s服务状态:"
if systemctl is-active --quiet k3s 2>/dev/null; then
    echo "✓ K3s服务运行正常"
    systemctl status k3s --no-pager -l
else
    echo "✗ K3s服务未运行"
fi

# 检查Matrix命名空间和资源
echo
echo "3. 检查Matrix命名空间和资源:"
if command -v kubectl >/dev/null 2>&1 && kubectl cluster-info >/dev/null 2>&1; then
    if kubectl get namespace matrix >/dev/null 2>&1; then
        echo "✓ Matrix命名空间存在"
        echo "Pod状态:"
        kubectl get pods -n matrix -o wide || echo "无Pod信息"
        echo "Service状态:"
        kubectl get services -n matrix || echo "无Service信息"
        echo "Deployment状态:"
        kubectl get deployments -n matrix || echo "无Deployment信息"
    else
        echo "✗ Matrix命名空间不存在"
    fi
else
    echo "✗ 无法检查Kubernetes资源"
fi

# 检查配置文件
echo
echo "4. 检查配置文件:"
if [[ -d ~/matrix ]]; then
    echo "✓ Matrix配置目录存在: ~/matrix"
    echo "配置文件列表:"
    find ~/matrix -type f -name "*.yaml" -o -name "*.conf" -o -name "*.py" -o -name "*.sh" | head -10
else
    echo "✗ Matrix配置目录不存在"
fi

# 检查监控服务
echo
echo "5. 检查监控服务:"
if systemctl list-units --type=service | grep -q matrix; then
    echo "✓ 发现Matrix相关服务"
    systemctl list-units --type=service | grep matrix
else
    echo "✗ 未发现Matrix相关服务"
fi

# 检查防火墙状态
echo
echo "6. 检查防火墙状态:"
if command -v ufw >/dev/null 2>&1; then
    echo "UFW状态:"
    sudo ufw status || echo "无法获取UFW状态"
elif command -v iptables >/dev/null 2>&1; then
    echo "iptables规则数量:"
    sudo iptables -L | wc -l || echo "无法获取iptables状态"
fi

# 生成测试报告
echo
echo "=== 生成测试报告 ==="
cat > matrix-deployment-test-report.txt << REPORT_EOF
Matrix部署测试报告
==================

测试环境:
- 服务器: **********
- 操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
- 测试时间: $(date)
- 部署耗时: ${MINUTES}分${SECONDS}秒

部署结果:
- Kubernetes集群: $(kubectl cluster-info >/dev/null 2>&1 && echo "正常" || echo "异常")
- K3s服务: $(systemctl is-active --quiet k3s 2>/dev/null && echo "运行中" || echo "未运行")
- Matrix命名空间: $(kubectl get namespace matrix >/dev/null 2>&1 && echo "存在" || echo "不存在")
- 配置目录: $([[ -d ~/matrix ]] && echo "存在" || echo "不存在")

详细日志: matrix-deployment-test.log
REPORT_EOF

echo "✓ 测试报告已生成: matrix-deployment-test-report.txt"

# 显示关键信息
echo
echo "=== 测试总结 ==="
echo "部署耗时: ${MINUTES}分${SECONDS}秒"
if [[ $DURATION -lt 60 ]]; then
    echo "⚠️  警告: 部署时间过短，可能没有执行真正的部署"
elif [[ $DURATION -gt 300 && $DURATION -lt 900 ]]; then
    echo "✓ 部署时间正常 (5-15分钟范围内)"
else
    echo "ℹ️  部署时间: ${MINUTES}分${SECONDS}秒"
fi

echo
echo "查看完整日志: cat matrix-deployment-test.log"
echo "查看测试报告: cat matrix-deployment-test-report.txt"
echo "测试完成时间: $(date)"
EOF

# 将脚本传输到服务器并执行
echo "正在将测试脚本传输到服务器..."
echo "服务器: **********"
echo "用户: jw"
echo

# 显示需要在服务器上执行的命令
cat << 'INSTRUCTIONS'
请在测试服务器上执行以下命令:

1. 登录服务器:
   ssh jw@**********
   # 密码: test123

2. 创建并执行测试脚本:
   cat > server_test_script.sh << 'EOF'
   [脚本内容已准备好]
   EOF
   
   chmod +x server_test_script.sh
   ./server_test_script.sh

3. 或者直接执行部署:
   bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)

4. 查看结果:
   cat matrix-deployment-test-report.txt
   cat matrix-deployment-test.log

INSTRUCTIONS

echo
echo "测试脚本已准备完成: server_test_script.sh"
echo "请将此脚本复制到服务器并执行以进行真实的部署测试。"
