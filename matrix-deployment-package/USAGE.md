# Matrix部署包使用指南

## 🎯 重要说明

**本部署包不依赖特定的仓库名或硬编码地址，可以放在任何Git仓库中使用。**

## 🚀 使用方法

### 方法1：一键安装（推荐）

```bash
# 第1步：设置部署源URL（必须）
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名"

# 第2步：执行安装
bash <(curl -fsSL https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名/setup.sh)
```

**具体示例**：
```bash
# GitHub示例
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/alice/my-matrix/main"
bash <(curl -fsSL https://raw.githubusercontent.com/alice/my-matrix/main/setup.sh)

# GitLab示例  
export DEPLOYMENT_BASE_URL="https://gitlab.com/bob/matrix-deploy/-/raw/main"
bash <(curl -fsSL https://gitlab.com/bob/matrix-deploy/-/raw/main/setup.sh)

# 自定义服务器示例
export DEPLOYMENT_BASE_URL="https://files.company.com/matrix"
bash <(curl -fsSL https://files.company.com/matrix/setup.sh)
```

### 方法2：本地执行

```bash
# 克隆您的仓库
git clone https://github.com/您的用户名/您的仓库名.git
cd 您的仓库名

# 直接执行
./setup.sh
```

### 方法3：临时指定URL

```bash
# 在命令中直接指定URL
DEPLOYMENT_BASE_URL="https://your-url" bash <(curl -fsSL https://your-url/setup.sh)
```

## ⚠️ 常见错误

### 错误1：`无法确定部署源URL`

**原因**：通过curl执行时未设置`DEPLOYMENT_BASE_URL`环境变量

**解决方案**：
```bash
# 必须先设置环境变量
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名"
bash <(curl -fsSL https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名/setup.sh)
```

### 错误2：`detect_base_url: command not found`

**原因**：使用了旧版本的脚本

**解决方案**：确保使用最新版本的setup.sh

### 错误3：模板文件下载失败

**原因**：`DEPLOYMENT_BASE_URL`设置错误或文件不存在

**解决方案**：
1. 检查URL是否正确
2. 确保仓库中包含所有必要文件
3. 检查网络连接

## 📁 仓库文件要求

您的仓库必须包含以下文件结构：

```
您的仓库/
├── setup.sh                    # 主安装脚本
├── test-setup.sh              # 测试脚本（可选）
├── templates/                  # 配置模板目录
│   ├── values.yaml.template
│   ├── nginx.conf.template
│   └── index.html.template
└── scripts/                    # 辅助脚本目录
    ├── routeros-api.py
    ├── virtual-ip-manager.sh
    └── validate-config.sh
```

## 🔧 测试部署

在正式部署前，可以使用测试脚本验证：

```bash
# 设置URL
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名"

# 运行测试
bash <(curl -fsSL https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名/test-setup.sh)
```

## 🌐 支持的部署源

### GitHub
```bash
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/用户名/仓库名/分支名"
```

### GitLab
```bash
export DEPLOYMENT_BASE_URL="https://gitlab.com/用户名/仓库名/-/raw/分支名"
```

### Gitee
```bash
export DEPLOYMENT_BASE_URL="https://gitee.com/用户名/仓库名/raw/分支名"
```

### 自定义服务器
```bash
export DEPLOYMENT_BASE_URL="https://your-domain.com/path/to/files"
```

## 📋 部署流程

1. **环境检测**：检测操作系统和架构
2. **服务器类型选择**：内部服务器或外部服务器
3. **配置收集**：交互式收集所有必要参数
4. **模板下载**：从指定URL下载配置模板
5. **配置生成**：根据用户输入生成配置文件
6. **服务部署**：安装和配置Matrix服务栈
7. **验证测试**：验证部署结果

## 🔒 安全注意事项

1. **URL验证**：确保`DEPLOYMENT_BASE_URL`指向可信的源
2. **HTTPS使用**：建议使用HTTPS URL
3. **权限检查**：脚本会要求sudo权限进行系统配置
4. **密码安全**：输入的密码会保存在配置文件中，注意文件权限

## 📞 故障排除

如果遇到问题：

1. **检查网络连接**
2. **验证URL正确性**
3. **确保仓库文件完整**
4. **查看日志文件**：`/tmp/matrix-deployment-*.log`
5. **使用本地执行方式**进行调试

## 🔄 更新部署包

要更新到最新版本：

```bash
# 重新设置URL并执行
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名"
bash <(curl -fsSL https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名/setup.sh)
```

---

**重要提醒**：本部署包设计为通用解决方案，不依赖特定的仓库名或硬编码地址，可以在任何Git仓库中使用。只需正确设置`DEPLOYMENT_BASE_URL`环境变量即可。
