# Matrix一键部署指南

## 🎯 真正的一键部署

现在您可以通过**一条命令**完成Matrix服务栈的完整部署，无需任何预配置！

## 🚀 使用方法

### 方法1：完全自动化（推荐）

```bash
# 一键部署 - 完全自动化
bash <(curl -fsSL https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名/setup.sh)
```

**特性**：
- ✅ 自动检测脚本来源URL
- ✅ 自动安装所有依赖（Docker、kubectl、Helm、K3s）
- ✅ 智能系统检测和适配
- ✅ 内置配置模板，无需外部文件
- ✅ 完整的进度显示和错误处理
- ✅ 自动回滚机制

### 方法2：静默安装模式

```bash
# 静默安装 - 无用户交互
SILENT_MODE=true bash <(curl -fsSL https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名/setup.sh)
```

### 方法3：自定义配置

```bash
# 设置环境变量进行自定义
export SILENT_MODE=false
export AUTO_INSTALL_DEPS=true
export MATRIX_HOME_DIR="/opt/matrix"
bash <(curl -fsSL https://raw.githubusercontent.com/您的用户名/您的仓库名/分支名/setup.sh)
```

## 🔧 支持的环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SILENT_MODE` | `false` | 静默安装模式，减少用户交互 |
| `AUTO_INSTALL_DEPS` | `false` | 自动安装依赖，无需确认 |
| `PROGRESS_MODE` | `true` | 显示进度条 |
| `ROLLBACK_ON_FAILURE` | `true` | 失败时自动回滚 |
| `MATRIX_HOME_DIR` | `~/matrix/` | Matrix安装目录 |
| `DEPLOYMENT_BASE_URL` | 自动检测 | 部署源URL（通常无需设置） |

## 🎛️ 部署流程

### 自动化步骤

1. **环境检测** - 检测操作系统、架构、网络环境
2. **依赖安装** - 自动安装Docker、kubectl、Helm、K3s等
3. **集群健康检查** - 验证K3s集群状态并自动修复
4. **配置收集** - 交互式收集必要的配置参数
5. **配置验证** - 验证配置的正确性和完整性
6. **服务部署** - 部署Matrix服务栈
7. **监控设置** - 设置虚拟IP监控和K3s健康监控
8. **完成验证** - 验证部署结果

### 用户输入项

即使在自动化模式下，您仍需要提供以下信息：

#### 基础配置
- **主域名**：example.com
- **服务器类型**：内部服务器 或 外部服务器
- **HTTPS端口**：8443（可自定义）
- **联邦端口**：8448（可自定义）

#### 内部服务器额外配置
- **RouterOS配置**：路由器IP、用户名、密码、WAN接口
- **SSL证书配置**：Cloudflare API Token、注册邮箱
- **端口配置**：WebRTC和TURN端口（使用默认值或自定义）

## 🔍 智能特性

### 1. 自动URL检测
- 从curl命令行参数推断仓库URL
- 支持GitHub、GitLab、自定义服务器
- 失败时自动启用自包含模式

### 2. 自包含模式
- 内置所有必要的配置模板
- 无需外部文件依赖
- 适用于任何网络环境

### 3. 依赖自动安装
- **Docker**：自动安装并配置用户组
- **kubectl**：下载最新版本并安装
- **Helm**：使用官方安装脚本
- **K3s**：自动安装并配置集群

### 4. 健康检查和修复
- K3s集群状态监控
- 异常Pod自动清理
- 服务自动重启
- 配置自动修复

### 5. 错误处理
- 详细的错误日志
- 自动回滚机制
- 清理临时文件
- 服务状态恢复

## 📊 进度显示

```
[====================] 100% - 完成部署
步骤 10/10: 完成部署
```

## 🔄 回滚机制

如果部署失败，脚本会自动：
- 停止已启动的服务
- 删除创建的文件和目录
- 清理systemd服务
- 恢复系统状态

## 🛠️ 故障排除

### 常见问题

1. **依赖安装失败**
   - 检查网络连接
   - 确保有sudo权限
   - 查看详细日志

2. **K3s启动失败**
   - 检查系统资源
   - 验证防火墙设置
   - 查看K3s日志

3. **配置验证失败**
   - 检查输入的参数
   - 验证网络连接
   - 确认RouterOS配置

### 日志文件

- **主日志**：`/tmp/matrix-deployment-*.log`
- **K3s监控**：`/var/log/k3s-monitor.log`
- **虚拟IP管理**：`~/matrix/logs/virtual-ip-manager.log`

## 🔐 安全注意事项

1. **权限要求**：脚本需要sudo权限安装系统依赖
2. **网络安全**：确保从可信源下载脚本
3. **密码安全**：输入的密码会保存在配置文件中
4. **防火墙**：脚本会自动配置防火墙规则

## 📋 系统要求

### 内部服务器
- **操作系统**：Ubuntu 20.04+、CentOS 8+、Debian 11+
- **内存**：最少2GB，推荐4GB+
- **CPU**：最少2核，推荐4核+
- **存储**：最少20GB可用空间
- **网络**：需要访问互联网

### 外部服务器
- **操作系统**：任何支持Nginx的Linux发行版
- **内存**：最少512MB
- **CPU**：最少1核
- **存储**：最少1GB可用空间
- **网络**：需要公网IP和域名

## 🎉 部署完成后

部署成功后，您将获得：

- **完整的Matrix服务栈**（8个核心组件）
- **自动化监控服务**（虚拟IP + K3s健康监控）
- **SSL证书配置指南**
- **服务管理命令**
- **详细的配置文档**

### 访问服务

- **主页导航**：https://您的域名:8443
- **Element Web**：https://chat.您的域名:8443
- **Matrix服务器**：https://matrix.您的域名:8443
- **账户管理**：https://account.您的域名:8443

---

**🎯 目标达成：真正的一键部署！**

现在您只需要执行一条命令，然后根据提示输入必要的配置信息，就可以完成Matrix服务栈的完整部署。无需手动安装依赖、无需预配置文件、无需复杂的设置过程！
