#!/bin/bash
# 测试修复后的setup.sh脚本

set -e

echo "=== 测试修复后的setup.sh脚本 ==="
echo "时间: $(date)"
echo

# 1. 语法检查
echo "1. 语法检查..."
if bash -n setup.sh; then
    echo "✓ 语法检查通过"
else
    echo "✗ 语法检查失败"
    exit 1
fi

# 2. 检查关键函数定义
echo "2. 检查关键函数定义..."
required_functions=("log_info" "log_error" "log_warn" "log_success" "install_docker")
for func in "${required_functions[@]}"; do
    if grep -q "^${func}()" setup.sh; then
        echo "✓ $func 函数已定义"
    else
        echo "✗ $func 函数缺失"
        exit 1
    fi
done

# 3. 检查变量引用
echo "3. 检查变量引用..."
if grep -q '\$OS_TYPE' setup.sh; then
    echo "✗ 仍有未修正的 \$OS_TYPE 引用"
    grep -n '\$OS_TYPE' setup.sh
    exit 1
else
    echo "✓ 所有 \$OS_TYPE 引用已修正"
fi

# 4. 检查os_type变量定义
echo "4. 检查os_type变量定义..."
if grep -q "local os_type" setup.sh; then
    echo "✓ os_type变量定义存在"
else
    echo "✗ os_type变量定义缺失"
    exit 1
fi

# 5. 模拟函数调用测试
echo "5. 模拟函数调用测试..."
# 创建临时测试脚本
cat > test_functions.sh << 'EOF'
#!/bin/bash
# 临时测试脚本

# 导入颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 导入LOG_FILE定义
LOG_FILE="/tmp/test-$$.log"

# 导入log函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

# 测试函数调用
echo "测试log函数..."
log_info "这是一个信息消息"
log_warn "这是一个警告消息"
log_error "这是一个错误消息"
log_success "这是一个成功消息"

echo "✓ 所有log函数调用正常"
EOF

chmod +x test_functions.sh
if ./test_functions.sh >/dev/null 2>&1; then
    echo "✓ 函数调用测试通过"
else
    echo "✗ 函数调用测试失败"
    exit 1
fi

# 清理测试文件
rm -f test_functions.sh /tmp/test-*.log

echo
echo "=== 所有测试通过！==="
echo "setup.sh脚本已修复，可以安全使用"
echo
echo "现在可以运行："
echo "./setup.sh"
echo
echo "或者上传到GitHub后使用："
echo "bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)"
