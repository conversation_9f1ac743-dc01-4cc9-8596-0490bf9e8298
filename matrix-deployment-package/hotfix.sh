#!/bin/bash
# Matrix部署脚本热修复
# 修正setup.sh中的变量和函数定义问题

set -e

SCRIPT_FILE="setup.sh"
BACKUP_FILE="setup.sh.backup.$(date +%s)"

echo "=== Matrix部署脚本热修复 ==="
echo "修复文件: $SCRIPT_FILE"
echo "备份文件: $BACKUP_FILE"
echo

# 备份原文件
if [[ -f "$SCRIPT_FILE" ]]; then
    cp "$SCRIPT_FILE" "$BACKUP_FILE"
    echo "✓ 已备份原文件"
else
    echo "错误: $SCRIPT_FILE 不存在"
    exit 1
fi

# 修复1: 替换未定义的log_success调用
echo "修复1: 替换log_success调用..."
sed -i 's/log_success "✓/echo "[SUCCESS] ✓/g' "$SCRIPT_FILE"

# 修复2: 修正OS_TYPE变量问题
echo "修复2: 修正OS_TYPE变量..."
sed -i '/install_docker() {/,/case "$OS_TYPE" in/ {
    /case "$OS_TYPE" in/i\
    # 检测操作系统类型\
    local os_type=""\
    if [[ -f /etc/os-release ]]; then\
        source /etc/os-release\
        os_type="$ID"\
    elif [[ -f /etc/debian_version ]]; then\
        os_type="debian"\
    elif [[ -f /etc/redhat-release ]]; then\
        os_type="centos"\
    else\
        os_type="unknown"\
    fi\

    s/case "$OS_TYPE" in/case "$os_type" in/
}' "$SCRIPT_FILE"

# 修复3: 替换OS_TYPE变量引用
echo "修复3: 替换OS_TYPE变量引用..."
sed -i 's/\$OS_TYPE/\$os_type/g' "$SCRIPT_FILE"

# 修复4: 修正systemctl检查
echo "修复4: 修正systemctl检查..."
sed -i 's/systemctl is-active --quiet k3s/systemctl is-active --quiet k3s 2>\/dev\/null/g' "$SCRIPT_FILE"

# 修复5: 添加缺失的log函数定义
echo "修复5: 添加缺失的log函数..."
if ! grep -q "log_success()" "$SCRIPT_FILE"; then
    # 在log_info函数后添加log_success函数
    sed -i '/^log_info() {/,/^}/ {
        /^}/a\
\
log_success() {\
    echo -e "${GREEN}[$(date '"'"'+%Y-%m-%d %H:%M:%S'"'"')] [SUCCESS]${NC} $*" | tee -a "$LOG_FILE"\
}
    }' "$SCRIPT_FILE"
fi

# 验证修复结果
echo
echo "验证修复结果..."

# 检查语法
if bash -n "$SCRIPT_FILE"; then
    echo "✓ 脚本语法检查通过"
else
    echo "✗ 脚本语法检查失败"
    echo "恢复备份文件..."
    cp "$BACKUP_FILE" "$SCRIPT_FILE"
    exit 1
fi

# 检查关键函数是否存在
if grep -q "log_success()" "$SCRIPT_FILE"; then
    echo "✓ log_success函数已添加"
else
    echo "⚠ log_success函数可能未正确添加"
fi

# 检查OS_TYPE修复
if grep -q "local os_type" "$SCRIPT_FILE"; then
    echo "✓ os_type变量定义已添加"
else
    echo "⚠ os_type变量定义可能未正确添加"
fi

echo
echo "=== 热修复完成 ==="
echo "备份文件: $BACKUP_FILE"
echo "现在可以运行: ./$SCRIPT_FILE"
echo
echo "如果修复有问题，可以恢复备份："
echo "cp $BACKUP_FILE $SCRIPT_FILE"
