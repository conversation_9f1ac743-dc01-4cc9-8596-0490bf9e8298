# Matrix服务栈混合部署解决方案

基于 `element-hq/ess-helm 25.6.2` 的Matrix服务栈混合部署解决方案，支持内网服务器 + 外部VPS的混合架构。

## 🎯 项目特色

### 核心优势
- **混合部署架构**：外部VPS提供导航 + 内部服务器运行服务
- **内置TURN服务器**：使用LiveKit内置TURN，完全自主，无外部依赖
- **动态IP适配**：RouterOS API监控，自动更新虚拟IP路由
- **一键部署**：`bash <(curl ...)` 极简安装
- **完整端口配置**：支持所有9个必要端口的自定义配置

### 解决的问题
- ✅ 内网服务器端口限制（支持自定义端口）
- ✅ 动态公网IP问题（虚拟IP + RouterOS监控）
- ✅ 外部TURN依赖（内置TURN服务器）
- ✅ 复杂部署流程（一键自动化部署）
- ✅ 配置错误风险（完整验证机制）

## 🏗️ 架构设计

```
[Matrix客户端] → [主域名well-known发现] → [直接连接内网服务器:自定义端口]
[网页访问] → [外部VPS导航页面] → [点击跳转到子域名] → [直接访问内网服务器:自定义端口]
                                      ↓
                              [虚拟IP路由系统]
                                      ↓
                              [动态公网IP监控] ← [RouterOS API每5秒检测]
```

### 服务器角色分工

#### 外部服务器（VPS）
- 🌐 主域名导航页面
- 🔍 Matrix服务发现（.well-known）
- 🔄 子域名重定向到内部服务器
- 💰 最小VPS即可（512MB内存）

#### 内部服务器（内网）
- 🏠 完整Matrix服务栈（8个核心组件）
- 🔄 RouterOS API WAN IP监控
- 🌐 虚拟IP路由管理
- 🔒 SSL证书管理
- 📞 内置TURN服务器

## 🚀 快速开始

### 一键安装

```bash
# 设置部署源（必须）- 替换为您的实际仓库地址
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/用户名/仓库名/分支名"

# 内部服务器部署
bash <(curl -fsSL https://raw.githubusercontent.com/用户名/仓库名/分支名/setup.sh)

# 或使用wget
bash <(wget -qO- https://raw.githubusercontent.com/用户名/仓库名/分支名/setup.sh)
```

**重要**：必须设置`DEPLOYMENT_BASE_URL`环境变量，脚本不依赖特定的仓库名或硬编码地址。

### 安装流程

1. **环境检测**：自动检测系统环境和依赖
2. **服务器类型选择**：内部服务器 或 外部服务器
3. **交互式配置**：收集所有必要配置参数
4. **配置验证**：验证配置的正确性和完整性
5. **自动部署**：根据服务器类型执行相应部署
6. **监控启动**：启动WAN IP监控和虚拟IP管理

## 📋 配置参数

### 基础配置
- **主域名**：example.com
- **HTTPS端口**：8443（可自定义）
- **联邦端口**：8448（可自定义）
- **安装目录**：~/matrix/（可自定义）

### 子域名配置
- **Synapse**：matrix.example.com
- **Element Web**：chat.example.com
- **MAS**：account.example.com
- **Matrix RTC**：mrtc.example.com

### 端口配置（内部服务器）
- **WebRTC TCP**：30881
- **WebRTC UDP多路复用**：30882
- **WebRTC UDP范围**：31000-32000
- **TURN UDP**：3478
- **TURN TCP**：3478
- **TURN TLS**：5349

### RouterOS配置（内部服务器）
- **路由器IP**：***********
- **API端口**：8728
- **用户名/密码**：admin/password
- **WAN接口**：ether1

### SSL证书配置
- **Cloudflare API Token**：用于DNS验证
- **注册邮箱**：<EMAIL>
- **隐私设置**：不暴露邮箱地址

## 🔧 核心技术

### TURN服务器配置
```yaml
# 正确的TURN配置
matrixRtc:
  enabled: true
  livekit:
    enabled: true
    turn:
      enabled: true              # 启用内置TURN
      stun_servers: []           # 禁用外部STUN
      turn_servers: []           # 禁用外部TURN
```

### 虚拟IP路由
- **LiveKit SFU**：**********
- **TURN服务器**：**********
- **动态路由更新**：每5秒检测IP变化
- **自动故障恢复**：IP变化时自动更新路由

### 防火墙配置
```bash
# 自动配置的防火墙规则
ufw allow 8443/tcp    # HTTPS
ufw allow 8448/tcp    # Federation
ufw allow 30881/tcp   # WebRTC TCP
ufw allow 30882/udp   # WebRTC UDP Mux
ufw allow 31000:32000/udp  # WebRTC UDP Range
ufw allow 3478/udp    # TURN UDP
ufw allow 3478/tcp    # TURN TCP
ufw allow 5349/tcp    # TURN TLS
```

## 📁 项目结构

```
matrix-deployment-package/
├── setup.sh                       # 一键安装脚本
├── templates/                      # 配置模板
│   ├── values.yaml.template       # Helm配置模板
│   ├── nginx.conf.template        # Nginx配置模板
│   └── index.html.template        # 主页模板
├── scripts/                        # 辅助脚本
│   ├── routeros-api.py            # RouterOS API客户端
│   ├── virtual-ip-manager.sh      # 虚拟IP管理
│   └── validate-config.sh         # 配置验证
└── README.md                       # 项目文档
```

## 🔍 配置验证

### 验证脚本
```bash
# 验证配置正确性
./scripts/validate-config.sh

# 跳过连接测试
./scripts/validate-config.sh --skip-connection-test
```

### 验证内容
- ✅ TURN服务器配置正确
- ✅ 外部依赖已禁用
- ✅ 端口配置完整
- ✅ RouterOS连接正常
- ✅ 防火墙规则正确
- ✅ SSL证书配置

## 🛠️ 管理命令

### 虚拟IP管理
```bash
# 启动监控服务
./config/virtual-ip-manager.sh start

# 停止监控服务
./config/virtual-ip-manager.sh stop

# 查看服务状态
./config/virtual-ip-manager.sh status

# 测试RouterOS连接
./config/virtual-ip-manager.sh test
```

### 服务管理
```bash
# 查看服务状态
systemctl status matrix-virtual-ip

# 查看日志
journalctl -u matrix-virtual-ip -f

# 重启服务
systemctl restart matrix-virtual-ip
```

## 📊 监控和日志

### 日志文件
- **部署日志**：`/tmp/matrix-deployment-*.log`
- **虚拟IP日志**：`~/matrix/logs/virtual-ip-manager.log`
- **IP变化记录**：`~/matrix/logs/ip-changes.log`
- **配置验证日志**：`~/matrix/logs/config-validation.log`

### 监控指标
- WAN IP变化检测（每5秒）
- 虚拟IP路由状态
- 防火墙规则状态
- 服务健康状态

## ⚠️ 重要注意事项

### TURN配置要点
1. **必须启用内置TURN**：`turn.enabled: true`
2. **必须禁用外部STUN**：`stun_servers: []`
3. **必须禁用外部TURN**：`turn_servers: []`
4. **完整端口配置**：确保所有9个端口都配置

### 常见问题
1. **WebRTC连接失败**：检查TURN配置和端口开放
2. **IP变化后服务中断**：检查RouterOS API连接
3. **证书申请失败**：检查Cloudflare API Token
4. **防火墙阻止连接**：检查ufw规则配置

## 🤝 贡献指南

### 开发环境
1. 克隆项目
2. 修改配置模板
3. 测试部署流程
4. 提交Pull Request

### 问题反馈
- 🐛 Bug报告：提供详细的错误日志
- 💡 功能建议：描述具体的使用场景
- 📖 文档改进：指出不清楚的地方

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🙏 致谢

- [element-hq/ess-helm](https://github.com/element-hq/ess-helm) - 上游项目
- [Matrix.org](https://matrix.org) - Matrix协议
- [LiveKit](https://livekit.io) - WebRTC基础设施
- [RouterOS](https://mikrotik.com) - 网络设备支持

---

**版本**：v1.0.0  
**基于**：element-hq/ess-helm 25.6.2  
**更新时间**：2025-06-21
