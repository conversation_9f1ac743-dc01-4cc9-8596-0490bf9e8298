#!/bin/bash
# Matrix一键修复和部署脚本
# 自动下载、修复并部署Matrix服务栈

set -e

echo "=== Matrix一键修复和部署脚本 ==="
echo "时间: $(date)"
echo

# 清理旧文件
echo "1. 清理环境..."
rm -f setup.sh setup.sh.* hotfix.sh

# 下载setup.sh
echo "2. 下载最新的setup.sh..."
if ! curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh -o setup.sh; then
    echo "错误: 无法下载setup.sh"
    exit 1
fi

echo "✓ 下载完成"

# 应用关键修复
echo "3. 应用关键修复..."

# 备份原文件
cp setup.sh setup.sh.original

# 修复1: 替换所有log_success调用
echo "修复log_success调用..."
sed -i 's/log_success "✓/echo "[SUCCESS] ✓/g' setup.sh
sed -i 's/log_success(/echo "[SUCCESS]" #log_success(/g' setup.sh

# 修复2: 在脚本开头添加LOG_FILE定义
echo "添加LOG_FILE定义..."
sed -i '2i\
LOG_FILE="/tmp/matrix-deployment-$$.log"' setup.sh

# 修复3: 添加简化的log函数
echo "添加简化的log函数..."
sed -i '/^# === 日志函数 ===/a\
log_success() {\
    echo -e "\\033[0;32m[SUCCESS]\\033[0m $*"\
}\
\
log_warn() {\
    echo -e "\\033[1;33m[WARN]\\033[0m $*"\
}\
' setup.sh

# 修复4: 修正install_docker函数
echo "修正install_docker函数..."
# 在install_docker函数中添加os_type检测
sed -i '/^install_docker() {/,/case.*in/ {
    /log_info "安装Docker..."/a\
    \
    # 检测操作系统类型\
    local os_type=""\
    if [[ -f /etc/os-release ]]; then\
        source /etc/os-release\
        os_type="$ID"\
    elif [[ -f /etc/debian_version ]]; then\
        os_type="debian"\
    elif [[ -f /etc/redhat-release ]]; then\
        os_type="centos"\
    else\
        os_type="unknown"\
    fi
    
    s/case "$OS_TYPE" in/case "$os_type" in/
}' setup.sh

# 替换所有OS_TYPE引用
sed -i 's/\$OS_TYPE/\$os_type/g' setup.sh

# 修复5: 修正systemctl检查
echo "修正systemctl检查..."
sed -i 's/systemctl is-active --quiet k3s/systemctl is-active --quiet k3s 2>\/dev\/null || true/g' setup.sh

# 修复6: 添加错误处理改进
echo "改进错误处理..."
sed -i 's/log_success "Docker安装完成"/echo "[SUCCESS] Docker安装完成"/g' setup.sh

echo "✓ 修复完成"

# 验证修复
echo "4. 验证修复结果..."
if bash -n setup.sh; then
    echo "✓ 脚本语法检查通过"
else
    echo "✗ 脚本语法检查失败，使用原始文件"
    cp setup.sh.original setup.sh
    exit 1
fi

# 设置执行权限
chmod +x setup.sh

echo "✓ 验证通过"

echo
echo "5. 开始部署..."
echo "执行修复后的setup.sh脚本..."
echo

# 执行修复后的脚本
exec ./setup.sh "$@"
