#!/bin/bash
# Matrix部署包测试脚本
# 验证部署包的完整性和功能正确性

set -euo pipefail

# === 配置变量 ===
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_LOG="/tmp/matrix-deployment-test-$(date +%s).log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# === 日志函数 ===
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $*" | tee -a "$TEST_LOG"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARN]${NC} $*" | tee -a "$TEST_LOG"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $*" | tee -a "$TEST_LOG"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS]${NC} $*" | tee -a "$TEST_LOG"
}

# === 测试函数 ===
test_file_structure() {
    log_info "测试文件结构..."
    
    local errors=0
    local required_files=(
        "setup.sh"
        "README.md"
        "templates/values.yaml.template"
        "templates/nginx.conf.template"
        "templates/index.html.template"
        "scripts/routeros-api.py"
        "scripts/virtual-ip-manager.sh"
        "scripts/validate-config.sh"
    )
    
    for file in "${required_files[@]}"; do
        local full_path="$PROJECT_ROOT/$file"
        if [[ -f "$full_path" ]]; then
            log_success "✓ $file 存在"
        else
            log_error "✗ $file 缺失"
            errors=$((errors + 1))
        fi
    done
    
    return $errors
}

test_script_permissions() {
    log_info "测试脚本权限..."
    
    local errors=0
    local executable_files=(
        "setup.sh"
        "scripts/routeros-api.py"
        "scripts/virtual-ip-manager.sh"
        "scripts/validate-config.sh"
    )
    
    for file in "${executable_files[@]}"; do
        local full_path="$PROJECT_ROOT/$file"
        if [[ -x "$full_path" ]]; then
            log_success "✓ $file 可执行"
        else
            log_warn "⚠ $file 不可执行，正在修复..."
            chmod +x "$full_path" 2>/dev/null || {
                log_error "✗ 无法设置 $file 为可执行"
                errors=$((errors + 1))
            }
        fi
    done
    
    return $errors
}

test_template_syntax() {
    log_info "测试模板语法..."
    
    local errors=0
    
    # 测试YAML模板
    local yaml_template="$PROJECT_ROOT/templates/values.yaml.template"
    if [[ -f "$yaml_template" ]]; then
        # 创建临时测试文件
        local temp_yaml="/tmp/test-values.yaml"
        
        # 替换变量为测试值
        sed -e 's/\${[^}]*}/test-value/g' "$yaml_template" > "$temp_yaml"
        
        # 验证YAML语法
        if command -v yq >/dev/null 2>&1; then
            if yq eval '.' "$temp_yaml" >/dev/null 2>&1; then
                log_success "✓ values.yaml.template 语法正确"
            else
                log_error "✗ values.yaml.template 语法错误"
                errors=$((errors + 1))
            fi
        elif python3 -c "import yaml" 2>/dev/null; then
            if python3 -c "import yaml; yaml.safe_load(open('$temp_yaml'))" 2>/dev/null; then
                log_success "✓ values.yaml.template 语法正确"
            else
                log_error "✗ values.yaml.template 语法错误"
                errors=$((errors + 1))
            fi
        else
            log_warn "⚠ 无法验证YAML语法（缺少yq或python3 yaml模块）"
        fi
        
        rm -f "$temp_yaml"
    fi
    
    # 测试Nginx配置模板
    local nginx_template="$PROJECT_ROOT/templates/nginx.conf.template"
    if [[ -f "$nginx_template" ]]; then
        # 简单的语法检查
        if grep -q "server {" "$nginx_template" && grep -q "location" "$nginx_template"; then
            log_success "✓ nginx.conf.template 基本语法正确"
        else
            log_error "✗ nginx.conf.template 语法可能有问题"
            errors=$((errors + 1))
        fi
    fi
    
    return $errors
}

test_turn_config() {
    log_info "测试TURN配置..."
    
    local errors=0
    local values_template="$PROJECT_ROOT/templates/values.yaml.template"
    
    if [[ -f "$values_template" ]]; then
        # 检查关键Matrix RTC配置
        local required_configs=(
            "matrixRTC:"
            "sfu:"
            "exposedServices:"
            "enabled: true"
        )

        for config in "${required_configs[@]}"; do
            if grep -q "$config" "$values_template"; then
                log_success "✓ 找到配置: $config"
            else
                log_error "✗ 缺少配置: $config"
                errors=$((errors + 1))
            fi
        done

        # 检查端口配置
        if grep -q "rtcTcp:" "$values_template" && grep -q "rtcMuxedUdp:" "$values_template"; then
            log_success "✓ 端口配置正确"
        else
            log_error "✗ 端口配置缺失或错误"
            errors=$((errors + 1))
        fi
    else
        log_error "✗ values.yaml.template 不存在"
        errors=$((errors + 1))
    fi
    
    return $errors
}

test_port_config() {
    log_info "测试端口配置..."
    
    local errors=0
    local setup_script="$PROJECT_ROOT/setup.sh"
    
    if [[ -f "$setup_script" ]]; then
        # 检查所有9个端口配置
        local required_ports=(
            "CUSTOM_HTTPS_PORT"
            "FEDERATION_PORT"
            "RTC_TCP_PORT"
            "RTC_MUXED_UDP_PORT"
            "RTC_UDP_START"
            "RTC_UDP_END"
            "TURN_UDP_PORT"
            "TURN_TCP_PORT"
            "TURN_TLS_PORT"
        )
        
        for port in "${required_ports[@]}"; do
            if grep -q "$port" "$setup_script"; then
                log_success "✓ 找到端口配置: $port"
            else
                log_error "✗ 缺少端口配置: $port"
                errors=$((errors + 1))
            fi
        done
    else
        log_error "✗ setup.sh 不存在"
        errors=$((errors + 1))
    fi
    
    return $errors
}

test_routeros_api() {
    log_info "测试RouterOS API脚本..."
    
    local errors=0
    local api_script="$PROJECT_ROOT/scripts/routeros-api.py"
    
    if [[ -f "$api_script" ]]; then
        # 检查Python语法
        if python3 -m py_compile "$api_script" 2>/dev/null; then
            log_success "✓ RouterOS API脚本语法正确"
        else
            log_error "✗ RouterOS API脚本语法错误"
            errors=$((errors + 1))
        fi
        
        # 检查关键函数
        local required_functions=(
            "get_wan_ip"
            "add_route"
            "remove_route"
            "connect"
            "disconnect"
        )
        
        for func in "${required_functions[@]}"; do
            if grep -q "def $func" "$api_script"; then
                log_success "✓ 找到函数: $func"
            else
                log_error "✗ 缺少函数: $func"
                errors=$((errors + 1))
            fi
        done
    else
        log_error "✗ routeros-api.py 不存在"
        errors=$((errors + 1))
    fi
    
    return $errors
}

test_virtual_ip_manager() {
    log_info "测试虚拟IP管理脚本..."
    
    local errors=0
    local manager_script="$PROJECT_ROOT/scripts/virtual-ip-manager.sh"
    
    if [[ -f "$manager_script" ]]; then
        # 检查Bash语法
        if bash -n "$manager_script" 2>/dev/null; then
            log_success "✓ 虚拟IP管理脚本语法正确"
        else
            log_error "✗ 虚拟IP管理脚本语法错误"
            errors=$((errors + 1))
        fi
        
        # 检查关键函数
        local required_functions=(
            "update_all_virtual_routes"
            "update_livekit_routes"
            "update_turn_routes"
            "configure_firewall"
            "start_monitoring"
        )
        
        for func in "${required_functions[@]}"; do
            if grep -q "$func()" "$manager_script"; then
                log_success "✓ 找到函数: $func"
            else
                log_error "✗ 缺少函数: $func"
                errors=$((errors + 1))
            fi
        done
    else
        log_error "✗ virtual-ip-manager.sh 不存在"
        errors=$((errors + 1))
    fi
    
    return $errors
}

test_config_validation() {
    log_info "测试配置验证脚本..."
    
    local errors=0
    local validate_script="$PROJECT_ROOT/scripts/validate-config.sh"
    
    if [[ -f "$validate_script" ]]; then
        # 检查Bash语法
        if bash -n "$validate_script" 2>/dev/null; then
            log_success "✓ 配置验证脚本语法正确"
        else
            log_error "✗ 配置验证脚本语法错误"
            errors=$((errors + 1))
        fi
        
        # 检查关键验证函数
        local required_functions=(
            "validate_turn_config"
            "validate_port_config"
            "validate_security_config"
            "validate_routeros_config"
            "test_routeros_connection"
        )
        
        for func in "${required_functions[@]}"; do
            if grep -q "$func()" "$validate_script"; then
                log_success "✓ 找到验证函数: $func"
            else
                log_error "✗ 缺少验证函数: $func"
                errors=$((errors + 1))
            fi
        done
    else
        log_error "✗ validate-config.sh 不存在"
        errors=$((errors + 1))
    fi
    
    return $errors
}

generate_test_report() {
    local total_errors="$1"
    
    echo
    echo "=== Matrix部署包测试报告 ==="
    echo "测试时间: $(date)"
    echo "项目目录: $PROJECT_ROOT"
    echo
    
    if [[ $total_errors -eq 0 ]]; then
        log_success "🎉 所有测试通过！"
        echo
        echo "✅ 文件结构完整"
        echo "✅ 脚本权限正确"
        echo "✅ 模板语法正确"
        echo "✅ TURN配置正确"
        echo "✅ 端口配置完整"
        echo "✅ RouterOS API功能完整"
        echo "✅ 虚拟IP管理功能完整"
        echo "✅ 配置验证功能完整"
        echo
        echo "部署包已准备就绪，可以进行部署！"
    else
        log_error "❌ 发现 $total_errors 个问题"
        echo
        echo "请根据上述错误信息修正问题后重新测试。"
    fi
    
    echo
    echo "详细日志: $TEST_LOG"
}

# === 主函数 ===
main() {
    echo "=== Matrix部署包测试工具 ==="
    echo "验证部署包的完整性和功能正确性"
    echo
    
    local total_errors=0
    
    # 执行所有测试
    test_file_structure
    total_errors=$((total_errors + $?))
    
    test_script_permissions
    total_errors=$((total_errors + $?))
    
    test_template_syntax
    total_errors=$((total_errors + $?))
    
    test_turn_config
    total_errors=$((total_errors + $?))
    
    test_port_config
    total_errors=$((total_errors + $?))
    
    test_routeros_api
    total_errors=$((total_errors + $?))
    
    test_virtual_ip_manager
    total_errors=$((total_errors + $?))
    
    test_config_validation
    total_errors=$((total_errors + $?))
    
    # 生成测试报告
    generate_test_report "$total_errors"
    
    exit $total_errors
}

# 执行主函数
main "$@"
