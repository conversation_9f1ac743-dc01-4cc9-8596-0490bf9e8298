#!/bin/bash
# Matrix配置验证脚本
# 确保TURN配置正确，外部依赖已禁用，所有端口配置完整

set -euo pipefail

# === 配置变量 ===
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MATRIX_HOME_DIR="${MATRIX_HOME_DIR:-$HOME/matrix/}"
CONFIG_FILE="${MATRIX_HOME_DIR}/config/matrix-values.yaml"
LOG_FILE="${MATRIX_HOME_DIR}/logs/config-validation.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# === 日志函数 ===
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $*" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARN]${NC} $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS]${NC} $*" | tee -a "$LOG_FILE"
}

# === 验证函数 ===
validate_file_exists() {
    local file="$1"
    local description="$2"
    
    if [[ -f "$file" ]]; then
        log_success "✓ $description 存在: $file"
        return 0
    else
        log_error "✗ $description 不存在: $file"
        return 1
    fi
}

validate_yaml_syntax() {
    local file="$1"
    
    log_info "验证YAML语法: $file"
    
    if command -v yq >/dev/null 2>&1; then
        if yq eval '.' "$file" >/dev/null 2>&1; then
            log_success "✓ YAML语法正确"
            return 0
        else
            log_error "✗ YAML语法错误"
            return 1
        fi
    elif python3 -c "import yaml" 2>/dev/null; then
        if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
            log_success "✓ YAML语法正确"
            return 0
        else
            log_error "✗ YAML语法错误"
            return 1
        fi
    else
        log_warn "无法验证YAML语法（缺少yq或python3 yaml模块）"
        return 0
    fi
}

validate_turn_config() {
    local file="$1"

    log_info "验证Matrix RTC配置..."

    local errors=0

    # 检查matrixRTC配置（注意大写RTC）
    if grep -q "matrixRTC:" "$file"; then
        log_success "✓ matrixRTC配置存在（拼写正确）"
    else
        log_error "✗ matrixRTC配置缺失或拼写错误"
        log_error "  应该是 matrixRTC: (大写RTC)，不是 matrixRtc:"
        errors=$((errors + 1))
    fi

    # 检查SFU配置
    if grep -q "sfu:" "$file" && grep -A 5 "sfu:" "$file" | grep -q "enabled: true"; then
        log_success "✓ LiveKit SFU已启用"
    else
        log_error "✗ LiveKit SFU未启用"
        errors=$((errors + 1))
    fi

    # 检查exposedServices配置
    if grep -q "exposedServices:" "$file"; then
        log_success "✓ exposedServices配置存在"

        # 检查rtcTcp端口
        if grep -q "rtcTcp:" "$file"; then
            log_success "✓ rtcTcp端口配置存在"
        else
            log_warn "⚠ rtcTcp端口配置可能缺失"
        fi

        # 检查rtcMuxedUdp端口
        if grep -q "rtcMuxedUdp:" "$file"; then
            log_success "✓ rtcMuxedUdp端口配置存在"
        else
            log_warn "⚠ rtcMuxedUdp端口配置可能缺失"
        fi
    else
        log_error "✗ exposedServices配置缺失"
        errors=$((errors + 1))
    fi

    # 检查TURN配置（在additional配置中）
    if grep -q "turn:" "$file" && grep -A 5 "turn:" "$file" | grep -q "enabled: true"; then
        log_success "✓ TURN服务器配置存在"
    else
        log_warn "⚠ TURN服务器配置可能缺失"
    fi

    return $errors
}

validate_port_config() {
    local file="$1"
    
    log_info "验证端口配置..."
    
    local errors=0
    local required_ports=(
        "CUSTOM_HTTPS_PORT"
        "FEDERATION_PORT"
        "RTC_TCP_PORT"
        "RTC_MUXED_UDP_PORT"
        "RTC_UDP_START"
        "RTC_UDP_END"
        "TURN_UDP_PORT"
        "TURN_TCP_PORT"
        "TURN_TLS_PORT"
    )
    
    for port_var in "${required_ports[@]}"; do
        if grep -q "$port_var" "$file"; then
            log_success "✓ $port_var 配置存在"
        else
            log_error "✗ $port_var 配置缺失"
            errors=$((errors + 1))
        fi
    done
    
    # 检查端口范围
    if grep -q "port_range_start" "$file" && grep -q "port_range_end" "$file"; then
        log_success "✓ WebRTC UDP端口范围配置存在"
    else
        log_warn "⚠ WebRTC UDP端口范围配置可能缺失"
    fi
    
    return $errors
}

validate_security_config() {
    local file="$1"
    
    log_info "验证安全配置..."
    
    local errors=0
    
    # 检查SSL配置
    if grep -q "ssl:" "$file" && grep -q "enabled: true" "$file"; then
        log_success "✓ SSL配置已启用"
    else
        log_warn "⚠ SSL配置可能未启用"
    fi
    
    # 检查防火墙配置
    if grep -q "firewall:" "$file"; then
        log_success "✓ 防火墙配置存在"
    else
        log_warn "⚠ 防火墙配置缺失"
    fi
    
    # 检查认证配置
    if grep -q "auth_secret" "$file"; then
        log_success "✓ TURN认证配置存在"
    else
        log_warn "⚠ TURN认证配置可能缺失"
    fi
    
    return $errors
}

validate_routeros_config() {
    log_info "验证RouterOS配置..."
    
    local config_file="${MATRIX_HOME_DIR}/config/virtual-routes.conf"
    local errors=0
    
    if [[ ! -f "$config_file" ]]; then
        log_error "✗ RouterOS配置文件不存在: $config_file"
        return 1
    fi
    
    source "$config_file" 2>/dev/null || {
        log_error "✗ RouterOS配置文件格式错误"
        return 1
    }
    
    # 检查必要配置
    local required_vars=(
        "ROUTER_IP"
        "ROUTER_PORT"
        "ROUTER_USER"
        "ROUTER_PASS"
        "WAN_INTERFACE"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -n "${!var:-}" ]]; then
            log_success "✓ $var 配置存在"
        else
            log_error "✗ $var 配置缺失"
            errors=$((errors + 1))
        fi
    done
    
    return $errors
}

test_routeros_connection() {
    log_info "测试RouterOS连接..."
    
    local api_script="${MATRIX_HOME_DIR}/config/routeros-api.py"
    
    if [[ ! -f "$api_script" ]]; then
        log_error "✗ RouterOS API脚本不存在: $api_script"
        return 1
    fi
    
    # 加载配置
    local config_file="${MATRIX_HOME_DIR}/config/virtual-routes.conf"
    if [[ -f "$config_file" ]]; then
        source "$config_file"
    else
        log_error "✗ RouterOS配置文件不存在"
        return 1
    fi
    
    # 测试连接
    if python3 "$api_script" "$ROUTER_IP" "$ROUTER_PORT" "$ROUTER_USER" "$ROUTER_PASS" "$WAN_INTERFACE" >/dev/null 2>&1; then
        log_success "✓ RouterOS连接测试成功"
        return 0
    else
        log_error "✗ RouterOS连接测试失败"
        log_error "  请检查RouterOS配置和网络连接"
        return 1
    fi
}

validate_firewall_rules() {
    log_info "验证防火墙规则..."
    
    if ! command -v ufw >/dev/null 2>&1; then
        log_warn "⚠ ufw未安装，跳过防火墙验证"
        return 0
    fi
    
    local errors=0
    local required_ports=(8443 8448 30881 30882 3478 5349)
    
    for port in "${required_ports[@]}"; do
        if sudo ufw status | grep -q "$port"; then
            log_success "✓ 端口 $port 防火墙规则存在"
        else
            log_warn "⚠ 端口 $port 防火墙规则可能缺失"
        fi
    done
    
    return $errors
}

generate_validation_report() {
    local total_errors="$1"
    
    echo
    echo "=== 配置验证报告 ==="
    echo "验证时间: $(date)"
    echo "配置文件: $CONFIG_FILE"
    echo
    
    if [[ $total_errors -eq 0 ]]; then
        log_success "🎉 所有配置验证通过！"
        echo
        echo "✅ TURN服务器配置正确"
        echo "✅ 外部依赖已禁用"
        echo "✅ 端口配置完整"
        echo "✅ 安全配置正确"
        echo "✅ RouterOS配置正确"
        echo
        echo "您的Matrix服务栈已准备就绪！"
    else
        log_error "❌ 发现 $total_errors 个配置问题"
        echo
        echo "请根据上述错误信息修正配置后重新验证。"
        echo
        echo "常见问题解决方案："
        echo "1. matrixRTC配置问题: 确保使用 matrixRTC: (大写RTC)，不是 matrixRtc:"
        echo "2. 端口配置问题: 检查所有9个端口是否正确配置"
        echo "3. RouterOS连接问题: 验证路由器IP、用户名、密码是否正确"
        echo "4. 防火墙问题: 运行 sudo ufw allow <端口>/tcp 或 /udp"
        echo "5. 配置结构问题: 确保使用上游项目支持的真实配置参数"
    fi
    
    echo
    echo "详细日志: $LOG_FILE"
}

# === 主函数 ===
main() {
    echo "=== Matrix配置验证工具 ==="
    echo "验证TURN配置、端口配置、安全配置等"
    echo
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    local total_errors=0
    
    # 验证配置文件存在
    if ! validate_file_exists "$CONFIG_FILE" "Matrix配置文件"; then
        echo "配置文件不存在，请先运行部署脚本"
        exit 1
    fi
    
    # 验证YAML语法
    if ! validate_yaml_syntax "$CONFIG_FILE"; then
        total_errors=$((total_errors + 1))
    fi
    
    # 验证TURN配置
    validate_turn_config "$CONFIG_FILE"
    total_errors=$((total_errors + $?))
    
    # 验证端口配置
    validate_port_config "$CONFIG_FILE"
    total_errors=$((total_errors + $?))
    
    # 验证安全配置
    validate_security_config "$CONFIG_FILE"
    total_errors=$((total_errors + $?))
    
    # 验证RouterOS配置
    validate_routeros_config
    total_errors=$((total_errors + $?))
    
    # 测试RouterOS连接
    if [[ "${1:-}" != "--skip-connection-test" ]]; then
        test_routeros_connection
        total_errors=$((total_errors + $?))
    fi
    
    # 验证防火墙规则
    validate_firewall_rules
    total_errors=$((total_errors + $?))
    
    # 生成验证报告
    generate_validation_report "$total_errors"
    
    exit $total_errors
}

# 执行主函数
main "$@"
