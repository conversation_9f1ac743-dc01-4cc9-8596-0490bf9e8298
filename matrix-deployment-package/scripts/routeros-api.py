#!/usr/bin/env python3
"""
RouterOS API客户端
用于Matrix项目的WAN IP监控和虚拟IP路由管理
基于RouterOS传统API（端口8728）
"""

import socket
import struct
import hashlib
import binascii
import time
import sys
import json
import logging
from typing import Optional, Dict, List, Any

class RouterOSAPI:
    """RouterOS API客户端类"""
    
    def __init__(self, host: str, port: int = 8728, timeout: int = 5):
        """
        初始化RouterOS API客户端
        
        Args:
            host: RouterOS设备IP地址
            port: API端口（默认8728）
            timeout: 连接超时时间（秒）
        """
        self.host = host
        self.port = port
        self.timeout = timeout
        self.socket = None
        self.connected = False
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def connect(self, username: str, password: str) -> bool:
        """
        连接到RouterOS设备并进行认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((self.host, self.port))
            
            self.logger.info(f"已连接到RouterOS: {self.host}:{self.port}")
            
            # 执行登录
            if self._login(username, password):
                self.connected = True
                self.logger.info("RouterOS API认证成功")
                return True
            else:
                self.logger.error("RouterOS API认证失败")
                self.disconnect()
                return False
                
        except Exception as e:
            self.logger.error(f"连接RouterOS失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        self.connected = False
        self.logger.info("已断开RouterOS连接")
    
    def _login(self, username: str, password: str) -> bool:
        """执行登录认证"""
        try:
            # 发送登录命令
            self._send_command(['/login'])
            response = self._read_response()
            
            if not response or response[0] != '!done':
                return False
            
            # 获取challenge
            challenge = None
            for item in response:
                if item.startswith('=ret='):
                    challenge = item[5:]
                    break
            
            if not challenge:
                return False
            
            # 计算响应
            challenge_bytes = binascii.unhexlify(challenge)
            password_bytes = password.encode('utf-8')
            
            md5 = hashlib.md5()
            md5.update(b'\x00')
            md5.update(password_bytes)
            md5.update(challenge_bytes)
            
            response_hash = binascii.hexlify(md5.digest()).decode('ascii')
            
            # 发送认证响应
            self._send_command(['/login', f'=name={username}', f'=response=00{response_hash}'])
            auth_response = self._read_response()
            
            return auth_response and auth_response[0] == '!done'
            
        except Exception as e:
            self.logger.error(f"登录过程出错: {e}")
            return False
    
    def _send_command(self, command: List[str]):
        """发送API命令"""
        for word in command:
            self._send_word(word)
        self._send_word('')  # 空字符串表示命令结束
    
    def _send_word(self, word: str):
        """发送单个词"""
        word_bytes = word.encode('utf-8')
        length = len(word_bytes)
        
        # 发送长度
        if length < 0x80:
            self.socket.send(struct.pack('!B', length))
        elif length < 0x4000:
            self.socket.send(struct.pack('!H', length | 0x8000))
        elif length < 0x200000:
            length_bytes = struct.pack('!I', length | 0xC00000)
            self.socket.send(length_bytes[1:])
        else:
            self.socket.send(struct.pack('!BI', 0xE0, length))
        
        # 发送数据
        if length > 0:
            self.socket.send(word_bytes)
    
    def _read_response(self) -> Optional[List[str]]:
        """读取API响应"""
        response = []
        while True:
            word = self._read_word()
            if word is None:
                return None
            if word == '':
                break
            response.append(word)
        return response
    
    def _read_word(self) -> Optional[str]:
        """读取单个词"""
        try:
            # 读取长度
            length_byte = self.socket.recv(1)
            if not length_byte:
                return None
            
            length = ord(length_byte)
            
            if length < 0x80:
                word_length = length
            elif length < 0xC0:
                next_byte = self.socket.recv(1)
                if not next_byte:
                    return None
                word_length = ((length & 0x7F) << 8) + ord(next_byte)
            elif length < 0xE0:
                next_bytes = self.socket.recv(2)
                if len(next_bytes) != 2:
                    return None
                word_length = ((length & 0x3F) << 16) + (ord(next_bytes[0]) << 8) + ord(next_bytes[1])
            else:
                next_bytes = self.socket.recv(4)
                if len(next_bytes) != 4:
                    return None
                word_length = struct.unpack('!I', next_bytes)[0]
            
            # 读取数据
            if word_length == 0:
                return ''
            
            word_bytes = b''
            while len(word_bytes) < word_length:
                chunk = self.socket.recv(word_length - len(word_bytes))
                if not chunk:
                    return None
                word_bytes += chunk
            
            return word_bytes.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"读取响应出错: {e}")
            return None
    
    def get_wan_ip(self, interface: str = 'ether1') -> Optional[str]:
        """
        获取WAN接口的公网IP地址
        
        Args:
            interface: WAN接口名称（默认ether1）
            
        Returns:
            str: 公网IP地址，获取失败返回None
        """
        if not self.connected:
            self.logger.error("未连接到RouterOS")
            return None
        
        try:
            # 查询接口IP地址
            self._send_command(['/ip/address/print', f'?interface={interface}'])
            response = self._read_response()
            
            if not response:
                return None
            
            # 解析响应
            for item in response:
                if item.startswith('=address='):
                    address = item[9:]  # 移除'=address='前缀
                    # 提取IP地址（移除子网掩码）
                    ip = address.split('/')[0]
                    self.logger.info(f"获取到WAN IP: {ip}")
                    return ip
            
            self.logger.warning(f"未找到接口 {interface} 的IP地址")
            return None
            
        except Exception as e:
            self.logger.error(f"获取WAN IP失败: {e}")
            return None
    
    def add_route(self, dst_address: str, gateway: str, comment: str = "") -> bool:
        """
        添加路由规则
        
        Args:
            dst_address: 目标地址
            gateway: 网关地址
            comment: 注释
            
        Returns:
            bool: 添加成功返回True
        """
        if not self.connected:
            self.logger.error("未连接到RouterOS")
            return False
        
        try:
            command = ['/ip/route/add', f'dst-address={dst_address}', f'gateway={gateway}']
            if comment:
                command.append(f'comment={comment}')
            
            self._send_command(command)
            response = self._read_response()
            
            success = response and response[0] == '!done'
            if success:
                self.logger.info(f"添加路由成功: {dst_address} -> {gateway}")
            else:
                self.logger.error(f"添加路由失败: {response}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"添加路由出错: {e}")
            return False
    
    def remove_route(self, dst_address: str) -> bool:
        """
        删除路由规则
        
        Args:
            dst_address: 目标地址
            
        Returns:
            bool: 删除成功返回True
        """
        if not self.connected:
            self.logger.error("未连接到RouterOS")
            return False
        
        try:
            # 查找路由ID
            self._send_command(['/ip/route/print', f'?dst-address={dst_address}'])
            response = self._read_response()
            
            if not response:
                return False
            
            route_id = None
            for item in response:
                if item.startswith('=.id='):
                    route_id = item[5:]
                    break
            
            if not route_id:
                self.logger.warning(f"未找到路由: {dst_address}")
                return True  # 路由不存在也算成功
            
            # 删除路由
            self._send_command(['/ip/route/remove', f'=.id={route_id}'])
            response = self._read_response()
            
            success = response and response[0] == '!done'
            if success:
                self.logger.info(f"删除路由成功: {dst_address}")
            else:
                self.logger.error(f"删除路由失败: {response}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"删除路由出错: {e}")
            return False


def main():
    """主函数 - 用于测试和命令行使用"""
    if len(sys.argv) < 6:
        print("用法: python3 routeros-api.py <host> <port> <username> <password> <wan_interface>")
        print("示例: python3 routeros-api.py *********** 8728 admin password ether1")
        sys.exit(1)
    
    host = sys.argv[1]
    port = int(sys.argv[2])
    username = sys.argv[3]
    password = sys.argv[4]
    wan_interface = sys.argv[5]
    
    # 创建API客户端
    api = RouterOSAPI(host, port)
    
    try:
        # 连接并认证
        if not api.connect(username, password):
            print("连接失败")
            sys.exit(1)
        
        # 获取WAN IP
        wan_ip = api.get_wan_ip(wan_interface)
        if wan_ip:
            print(f"WAN IP: {wan_ip}")
            
            # 输出JSON格式（便于脚本调用）
            result = {
                "success": True,
                "wan_ip": wan_ip,
                "timestamp": int(time.time())
            }
            print(json.dumps(result))
        else:
            result = {
                "success": False,
                "error": "无法获取WAN IP",
                "timestamp": int(time.time())
            }
            print(json.dumps(result))
            sys.exit(1)
    
    finally:
        api.disconnect()


if __name__ == "__main__":
    main()
