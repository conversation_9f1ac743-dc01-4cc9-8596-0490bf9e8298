#!/bin/bash
# 虚拟IP路由管理脚本
# 用于Matrix项目的动态IP监控和路由更新
# 支持LiveKit和TURN服务器的虚拟IP路由

set -euo pipefail

# === 配置变量 ===
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/../config/virtual-routes.conf"
LOG_FILE="${MATRIX_HOME_DIR:-$HOME/matrix}/logs/virtual-ip-manager.log"
PID_FILE="/tmp/virtual-ip-manager.pid"

# 默认配置
LIVEKIT_VIRTUAL_IP="**********"
TURN_VIRTUAL_IP="**********"
CHECK_INTERVAL=5
ROUTER_API_SCRIPT="${SCRIPT_DIR}/routeros-api.py"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# === 日志函数 ===
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $*" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARN]${NC} $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $*" | tee -a "$LOG_FILE"
}

# === 配置加载函数 ===
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        log_info "加载配置文件: $CONFIG_FILE"
        source "$CONFIG_FILE"
    else
        log_warn "配置文件不存在，使用默认配置: $CONFIG_FILE"
        create_default_config
    fi
    
    # 验证必要配置
    if [[ -z "${ROUTER_IP:-}" ]] || [[ -z "${ROUTER_USER:-}" ]] || [[ -z "${ROUTER_PASS:-}" ]]; then
        log_error "RouterOS配置不完整，请检查配置文件"
        exit 1
    fi
}

create_default_config() {
    log_info "创建默认配置文件..."
    
    mkdir -p "$(dirname "$CONFIG_FILE")"
    
    cat > "$CONFIG_FILE" << EOF
# 虚拟IP路由管理配置文件
# 由Matrix部署脚本自动生成

# RouterOS API配置
ROUTER_IP="***********"
ROUTER_PORT="8728"
ROUTER_USER="admin"
ROUTER_PASS="password"
WAN_INTERFACE="ether1"
TIMEOUT="5"

# 虚拟IP配置
LIVEKIT_VIRTUAL_IP="**********"
TURN_VIRTUAL_IP="**********"

# 监控配置
CHECK_INTERVAL=5
MAX_RETRIES=3
RETRY_DELAY=2

# 端口配置
RTC_TCP_PORT="30881"
RTC_MUXED_UDP_PORT="30882"
RTC_UDP_START="31000"
RTC_UDP_END="32000"
TURN_UDP_PORT="3478"
TURN_TCP_PORT="3478"
TURN_TLS_PORT="5349"

# 日志配置
LOG_LEVEL="INFO"
LOG_RETENTION_DAYS=7
EOF
    
    log_warn "请编辑配置文件并设置正确的RouterOS参数: $CONFIG_FILE"
}

# === RouterOS API函数 ===
get_wan_ip() {
    local retry_count=0
    local max_retries=${MAX_RETRIES:-3}
    
    while [[ $retry_count -lt $max_retries ]]; do
        if [[ -f "$ROUTER_API_SCRIPT" ]]; then
            # 使用Python API脚本
            local result
            result=$(python3 "$ROUTER_API_SCRIPT" \
                "$ROUTER_IP" "$ROUTER_PORT" "$ROUTER_USER" "$ROUTER_PASS" "$WAN_INTERFACE" 2>/dev/null)
            
            if [[ $? -eq 0 ]] && [[ -n "$result" ]]; then
                # 解析JSON结果
                local wan_ip
                wan_ip=$(echo "$result" | grep -o '"wan_ip":"[^"]*"' | cut -d'"' -f4)
                if [[ -n "$wan_ip" ]]; then
                    echo "$wan_ip"
                    return 0
                fi
            fi
        fi
        
        retry_count=$((retry_count + 1))
        if [[ $retry_count -lt $max_retries ]]; then
            log_warn "获取WAN IP失败，重试 $retry_count/$max_retries..."
            sleep "${RETRY_DELAY:-2}"
        fi
    done
    
    log_error "获取WAN IP失败，已重试 $max_retries 次"
    return 1
}

# === 路由管理函数 ===
update_virtual_ip_route() {
    local virtual_ip="$1"
    local real_ip="$2"
    local description="$3"
    
    log_info "更新虚拟IP路由: $virtual_ip -> $real_ip ($description)"
    
    # 删除旧路由（如果存在）
    if ip route show | grep -q "$virtual_ip"; then
        log_info "删除旧的虚拟IP路由: $virtual_ip"
        sudo ip route del "$virtual_ip" 2>/dev/null || true
    fi
    
    # 添加新路由
    if sudo ip route add "$virtual_ip" via "$real_ip" 2>/dev/null; then
        log_info "✓ 虚拟IP路由更新成功: $virtual_ip -> $real_ip"
        return 0
    else
        log_error "✗ 虚拟IP路由更新失败: $virtual_ip -> $real_ip"
        return 1
    fi
}

update_all_virtual_routes() {
    local new_wan_ip="$1"
    local success=true

    log_info "更新所有虚拟IP路由到新的WAN IP: $new_wan_ip"

    # 更新LiveKit SFU虚拟IP路由（支持WebRTC端口）
    if ! update_livekit_routes "$new_wan_ip"; then
        success=false
    fi

    # 更新TURN服务器虚拟IP路由（支持TURN端口）
    if ! update_turn_routes "$new_wan_ip"; then
        success=false
    fi

    if $success; then
        log_info "✓ 所有虚拟IP路由更新完成"
        return 0
    else
        log_error "✗ 部分虚拟IP路由更新失败"
        return 1
    fi
}

update_livekit_routes() {
    local new_wan_ip="$1"
    local success=true

    log_info "更新LiveKit SFU路由 ($LIVEKIT_VIRTUAL_IP -> $new_wan_ip)"

    # 基础虚拟IP路由
    if ! update_virtual_ip_route "$LIVEKIT_VIRTUAL_IP" "$new_wan_ip" "LiveKit SFU"; then
        success=false
    fi

    # WebRTC端口路由（使用iptables DNAT）
    update_port_route "$LIVEKIT_VIRTUAL_IP" "$new_wan_ip" "${RTC_TCP_PORT:-30881}" "tcp" "WebRTC TCP"
    update_port_route "$LIVEKIT_VIRTUAL_IP" "$new_wan_ip" "${RTC_MUXED_UDP_PORT:-30882}" "udp" "WebRTC UDP Mux"

    # WebRTC UDP端口范围
    local udp_start="${RTC_UDP_START:-31000}"
    local udp_end="${RTC_UDP_END:-32000}"
    update_port_range_route "$LIVEKIT_VIRTUAL_IP" "$new_wan_ip" "$udp_start" "$udp_end" "udp" "WebRTC UDP Range"

    if $success; then
        log_info "✓ LiveKit SFU路由更新完成"
        return 0
    else
        log_error "✗ LiveKit SFU路由更新失败"
        return 1
    fi
}

update_turn_routes() {
    local new_wan_ip="$1"
    local success=true

    log_info "更新TURN服务器路由 ($TURN_VIRTUAL_IP -> $new_wan_ip)"

    # 基础虚拟IP路由
    if ! update_virtual_ip_route "$TURN_VIRTUAL_IP" "$new_wan_ip" "TURN Server"; then
        success=false
    fi

    # TURN端口路由（使用iptables DNAT）
    update_port_route "$TURN_VIRTUAL_IP" "$new_wan_ip" "${TURN_UDP_PORT:-3478}" "udp" "TURN UDP"
    update_port_route "$TURN_VIRTUAL_IP" "$new_wan_ip" "${TURN_TCP_PORT:-3478}" "tcp" "TURN TCP"
    update_port_route "$TURN_VIRTUAL_IP" "$new_wan_ip" "${TURN_TLS_PORT:-5349}" "tcp" "TURN TLS"

    if $success; then
        log_info "✓ TURN服务器路由更新完成"
        return 0
    else
        log_error "✗ TURN服务器路由更新失败"
        return 1
    fi
}

update_port_route() {
    local virtual_ip="$1"
    local real_ip="$2"
    local port="$3"
    local protocol="$4"
    local description="$5"

    log_info "更新端口路由: $virtual_ip:$port/$protocol -> $real_ip:$port ($description)"

    # 删除旧的DNAT规则
    sudo iptables -t nat -D PREROUTING -d "$virtual_ip" -p "$protocol" --dport "$port" -j DNAT --to-destination "$real_ip:$port" 2>/dev/null || true

    # 添加新的DNAT规则
    if sudo iptables -t nat -A PREROUTING -d "$virtual_ip" -p "$protocol" --dport "$port" -j DNAT --to-destination "$real_ip:$port" 2>/dev/null; then
        log_info "✓ 端口路由更新成功: $virtual_ip:$port/$protocol -> $real_ip:$port"
        return 0
    else
        log_error "✗ 端口路由更新失败: $virtual_ip:$port/$protocol -> $real_ip:$port"
        return 1
    fi
}

update_port_range_route() {
    local virtual_ip="$1"
    local real_ip="$2"
    local start_port="$3"
    local end_port="$4"
    local protocol="$5"
    local description="$6"

    log_info "更新端口范围路由: $virtual_ip:$start_port-$end_port/$protocol -> $real_ip ($description)"

    # 删除旧的DNAT规则
    sudo iptables -t nat -D PREROUTING -d "$virtual_ip" -p "$protocol" --dport "$start_port:$end_port" -j DNAT --to-destination "$real_ip" 2>/dev/null || true

    # 添加新的DNAT规则
    if sudo iptables -t nat -A PREROUTING -d "$virtual_ip" -p "$protocol" --dport "$start_port:$end_port" -j DNAT --to-destination "$real_ip" 2>/dev/null; then
        log_info "✓ 端口范围路由更新成功: $virtual_ip:$start_port-$end_port/$protocol -> $real_ip"
        return 0
    else
        log_error "✗ 端口范围路由更新失败: $virtual_ip:$start_port-$end_port/$protocol -> $real_ip"
        return 1
    fi
}

# === 防火墙管理函数 ===
configure_firewall() {
    log_info "配置防火墙规则..."
    
    # 检查ufw是否可用
    if ! command -v ufw >/dev/null 2>&1; then
        log_warn "ufw未安装，跳过防火墙配置"
        return 0
    fi
    
    # Web服务端口
    sudo ufw allow "${CUSTOM_HTTPS_PORT:-8443}/tcp" comment "Matrix HTTPS" 2>/dev/null || true
    sudo ufw allow "${FEDERATION_PORT:-8448}/tcp" comment "Matrix Federation" 2>/dev/null || true
    
    # LiveKit WebRTC端口
    sudo ufw allow "${RTC_TCP_PORT:-30881}/tcp" comment "WebRTC TCP" 2>/dev/null || true
    sudo ufw allow "${RTC_MUXED_UDP_PORT:-30882}/udp" comment "WebRTC UDP Mux" 2>/dev/null || true
    sudo ufw allow "${RTC_UDP_START:-31000}:${RTC_UDP_END:-32000}/udp" comment "WebRTC UDP Range" 2>/dev/null || true
    
    # TURN服务器端口
    sudo ufw allow "${TURN_UDP_PORT:-3478}/udp" comment "TURN UDP" 2>/dev/null || true
    sudo ufw allow "${TURN_TCP_PORT:-3478}/tcp" comment "TURN TCP" 2>/dev/null || true
    sudo ufw allow "${TURN_TLS_PORT:-5349}/tcp" comment "TURN TLS" 2>/dev/null || true
    
    log_info "✓ 防火墙规则配置完成"
}

# === 监控函数 ===
start_monitoring() {
    log_info "启动WAN IP监控服务..."
    
    # 检查是否已经在运行
    if [[ -f "$PID_FILE" ]]; then
        local old_pid
        old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            log_warn "监控服务已在运行 (PID: $old_pid)"
            return 0
        else
            log_info "清理旧的PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 获取初始WAN IP
    local current_wan_ip
    current_wan_ip=$(get_wan_ip)
    if [[ -z "$current_wan_ip" ]]; then
        log_error "无法获取初始WAN IP，监控服务启动失败"
        return 1
    fi
    
    log_info "初始WAN IP: $current_wan_ip"
    
    # 初始化虚拟IP路由
    update_all_virtual_routes "$current_wan_ip"
    
    # 配置防火墙
    configure_firewall
    
    # 保存PID
    echo $$ > "$PID_FILE"
    
    log_info "WAN IP监控服务已启动 (PID: $$)"
    log_info "检查间隔: ${CHECK_INTERVAL}秒"
    
    # 监控循环
    while true; do
        sleep "$CHECK_INTERVAL"
        
        local new_wan_ip
        new_wan_ip=$(get_wan_ip)
        
        if [[ -z "$new_wan_ip" ]]; then
            log_warn "无法获取WAN IP，跳过本次检查"
            continue
        fi
        
        if [[ "$new_wan_ip" != "$current_wan_ip" ]]; then
            log_info "检测到WAN IP变化: $current_wan_ip -> $new_wan_ip"
            
            if update_all_virtual_routes "$new_wan_ip"; then
                current_wan_ip="$new_wan_ip"
                log_info "WAN IP变化处理完成"
                
                # 记录IP变化历史
                echo "$(date '+%Y-%m-%d %H:%M:%S') $current_wan_ip -> $new_wan_ip" >> \
                    "${MATRIX_HOME_DIR:-$HOME/matrix}/logs/ip-changes.log"
            else
                log_error "WAN IP变化处理失败，保持当前配置"
            fi
        fi
    done
}

stop_monitoring() {
    log_info "停止WAN IP监控服务..."
    
    if [[ -f "$PID_FILE" ]]; then
        local pid
        pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            rm -f "$PID_FILE"
            log_info "监控服务已停止 (PID: $pid)"
        else
            log_warn "监控服务未运行"
            rm -f "$PID_FILE"
        fi
    else
        log_warn "未找到PID文件，监控服务可能未运行"
    fi
}

status_monitoring() {
    if [[ -f "$PID_FILE" ]]; then
        local pid
        pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "监控服务正在运行 (PID: $pid)"
            return 0
        else
            echo "监控服务未运行（PID文件存在但进程不存在）"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        echo "监控服务未运行"
        return 1
    fi
}

# === 主函数 ===
main() {
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 加载配置
    load_config
    
    case "${1:-start}" in
        start)
            start_monitoring
            ;;
        stop)
            stop_monitoring
            ;;
        restart)
            stop_monitoring
            sleep 2
            start_monitoring
            ;;
        status)
            status_monitoring
            ;;
        test)
            log_info "测试RouterOS连接..."
            local wan_ip
            wan_ip=$(get_wan_ip)
            if [[ -n "$wan_ip" ]]; then
                log_info "✓ RouterOS连接正常，当前WAN IP: $wan_ip"
            else
                log_error "✗ RouterOS连接失败"
                exit 1
            fi
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|test}"
            echo "  start   - 启动WAN IP监控服务"
            echo "  stop    - 停止WAN IP监控服务"
            echo "  restart - 重启WAN IP监控服务"
            echo "  status  - 查看监控服务状态"
            echo "  test    - 测试RouterOS连接"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
