# Nginx配置模板 - 外部服务器
# 用于Matrix混合部署架构的外部VPS配置
# 提供主域名导航页面和Matrix服务发现

# 主域名配置 - 导航页面
server {
    listen 80;
    listen [::]:80;
    server_name ${DOMAIN};
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ${DOMAIN};
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/${DOMAIN}.crt;
    ssl_certificate_key /etc/ssl/private/${DOMAIN}.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 网站根目录
    root /var/www/html;
    index index.html index.htm;
    
    # 主页面
    location / {
        try_files $uri $uri/ =404;
    }
    
    # Matrix服务发现配置
    location /.well-known/matrix/server {
        default_type application/json;
        add_header Access-Control-Allow-Origin *;
        return 200 '{"m.server": "${MATRIX_SUBDOMAIN}.${DOMAIN}:${FEDERATION_PORT}"}';
    }
    
    location /.well-known/matrix/client {
        default_type application/json;
        add_header Access-Control-Allow-Origin *;
        return 200 '{
            "m.homeserver": {
                "base_url": "https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"
            },
            "m.identity_server": {
                "base_url": "https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"
            },
            "m.integrations": {
                "managers": [
                    {
                        "api_url": "https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}",
                        "ui_url": "https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"
                    }
                ]
            }
        }';
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "OK\n";
        add_header Content-Type text/plain;
    }
}

# Synapse (Matrix服务器) 重定向
server {
    listen 80;
    listen [::]:80;
    server_name ${MATRIX_SUBDOMAIN}.${DOMAIN};
    return 301 https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ${MATRIX_SUBDOMAIN}.${DOMAIN};
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/${MATRIX_SUBDOMAIN}.${DOMAIN}.crt;
    ssl_certificate_key /etc/ssl/private/${MATRIX_SUBDOMAIN}.${DOMAIN}.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 重定向到内部服务器的自定义端口
    return 301 https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}$request_uri;
}

# Element Web 重定向
server {
    listen 80;
    listen [::]:80;
    server_name ${ELEMENT_SUBDOMAIN}.${DOMAIN};
    return 301 https://${ELEMENT_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ${ELEMENT_SUBDOMAIN}.${DOMAIN};
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/${ELEMENT_SUBDOMAIN}.${DOMAIN}.crt;
    ssl_certificate_key /etc/ssl/private/${ELEMENT_SUBDOMAIN}.${DOMAIN}.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 重定向到内部服务器的自定义端口
    return 301 https://${ELEMENT_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}$request_uri;
}

# Matrix Authentication Service 重定向
server {
    listen 80;
    listen [::]:80;
    server_name ${MAS_SUBDOMAIN}.${DOMAIN};
    return 301 https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ${MAS_SUBDOMAIN}.${DOMAIN};
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/${MAS_SUBDOMAIN}.${DOMAIN}.crt;
    ssl_certificate_key /etc/ssl/private/${MAS_SUBDOMAIN}.${DOMAIN}.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 重定向到内部服务器的自定义端口
    return 301 https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}$request_uri;
}

# Matrix RTC 重定向
server {
    listen 80;
    listen [::]:80;
    server_name ${MRTC_SUBDOMAIN}.${DOMAIN};
    return 301 https://${MRTC_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ${MRTC_SUBDOMAIN}.${DOMAIN};
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/${MRTC_SUBDOMAIN}.${DOMAIN}.crt;
    ssl_certificate_key /etc/ssl/private/${MRTC_SUBDOMAIN}.${DOMAIN}.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 重定向到内部服务器的自定义端口
    return 301 https://${MRTC_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}$request_uri;
}

# 通用配置
# 隐藏Nginx版本
server_tokens off;

# 日志配置
access_log /var/log/nginx/matrix-access.log;
error_log /var/log/nginx/matrix-error.log;

# Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;
