# Matrix服务栈Helm配置模板
# 基于element-hq/ess-helm 25.6.2修改
# 支持混合部署架构和内置TURN服务器

global:
  serverName: "${DOMAIN}"
  
# 安装目录配置
persistence:
  homeDir: "${MATRIX_HOME_DIR}"

# Synapse配置
synapse:
  enabled: true
  ingress:
    hostname: "${MATRIX_SUBDOMAIN}.${DOMAIN}"
    port: ${CUSTOM_HTTPS_PORT}
  persistence:
    storageClass: "local-path"
    dataDir: "${MATRIX_HOME_DIR}/synapse"
  # 联邦配置
  federation:
    enabled: true
    port: ${FEDERATION_PORT}
    verify_keys: true
    allow_public_rooms: true
    federation_domain_whitelist: []
  # 服务器配置
  server:
    name: "${DOMAIN}"
    public_baseurl: "https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"
  # 数据库配置
  database:
    host: "postgresql"
    port: 5432
    name: "synapse"
    user: "synapse"

# Element Web配置
elementWeb:
  enabled: true
  ingress:
    hostname: "${ELEMENT_SUBDOMAIN}.${DOMAIN}"
    port: ${CUSTOM_HTTPS_PORT}
  config:
    default_server_config:
      "m.homeserver":
        base_url: "https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"
        server_name: "${DOMAIN}"
      "m.identity_server":
        base_url: "https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"

# Matrix Authentication Service配置
matrixAuthenticationService:
  enabled: true
  ingress:
    hostname: "${MAS_SUBDOMAIN}.${DOMAIN}"
    port: ${CUSTOM_HTTPS_PORT}
  config:
    issuer: "https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}/"
    matrix:
      homeserver: "https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"

# PostgreSQL数据库配置
postgresql:
  enabled: true
  persistence:
    dataDir: "${MATRIX_HOME_DIR}/postgresql"
    storageClass: "local-path"
  auth:
    postgresPassword: "postgres"
    database: "synapse"
    username: "synapse"
    password: "synapse"

# Matrix RTC配置（基于上游项目真实配置结构）
matrixRTC:
  enabled: true
  ingress:
    hostname: "${MRTC_SUBDOMAIN}.${DOMAIN}"
    port: ${CUSTOM_HTTPS_PORT}

  # LiveKit SFU配置（基于上游项目真实结构）
  sfu:
    enabled: true
    # 暴露的服务端口配置
    exposedServices:
      rtcTcp:
        enabled: true
        portType: NodePort
        port: ${RTC_TCP_PORT}
      rtcMuxedUdp:
        enabled: true
        portType: NodePort
        port: ${RTC_MUXED_UDP_PORT}
      rtcUdp:
        enabled: true
        portType: NodePort
        portRange:
          startPort: ${RTC_UDP_START}
          endPort: ${RTC_UDP_END}

    # LiveKit额外配置（基于官方文档）
    additional:
      0-turn-config:
        config: |
          turn:
            enabled: true
            domain: "${MRTC_SUBDOMAIN}.${DOMAIN}"
            udp_port: ${TURN_UDP_PORT}
            tls_port: ${TURN_TLS_PORT}
          rtc:
            use_external_ip: true
            tcp_port: ${RTC_TCP_PORT}
            port_range_start: ${RTC_UDP_START}
            port_range_end: ${RTC_UDP_END}

# HAProxy负载均衡器配置
haproxy:
  enabled: true
  service:
    type: "NodePort"
    port: ${CUSTOM_HTTPS_PORT}
    nodePort: 30443
  config:
    frontend:
      bind: "*:${CUSTOM_HTTPS_PORT}"
    backend:
      servers:
        - name: "synapse"
          address: "synapse:8008"
        - name: "element"
          address: "element-web:8080"
        - name: "mas"
          address: "mas:8080"

# Well-known委托配置
wellKnown:
  enabled: true
  server:
    "m.server": "${MATRIX_SUBDOMAIN}.${DOMAIN}:${FEDERATION_PORT}"
  client:
    "m.homeserver":
      base_url: "https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"
    "m.identity_server":
      base_url: "https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}"

# SSL证书配置
ssl:
  enabled: true
  certificatesDir: "${MATRIX_HOME_DIR}/ssl"
  issuer: "letsencrypt"
  cloudflare:
    apiToken: "${CLOUDFLARE_API_TOKEN}"
  acme:
    email: "${ACME_EMAIL}"
    server: "https://acme-v02.api.letsencrypt.org/directory"
    # 不同意暴露邮箱地址
    agreeToTerms: true
    exposeEmail: false

# 日志配置
logging:
  enabled: true
  logDir: "${MATRIX_HOME_DIR}/logs"
  level: "INFO"
  retention: "30d"

# 监控配置
monitoring:
  enabled: true
  prometheus:
    enabled: true
  grafana:
    enabled: true

# 注释：删除了虚假的配置段
# networking、routerOS、security、backup 等配置段在上游项目中不存在
# 这些功能通过外部脚本和系统配置管理
