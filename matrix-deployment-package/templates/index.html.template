<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matrix服务 - ${DOMAIN}</title>
    <meta name="description" content="Matrix去中心化通信服务 - 安全、私密、开源的即时通讯平台">
    
    <style>
        /* 基于matrix.org官方网站的配色方案 */
        :root {
            --primary-color: #0DBD8B;
            --secondary-color: #368BD6;
            --dark-color: #2D2D2D;
            --light-color: #F7F7F7;
            --text-color: #333;
            --border-color: #E0E0E0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, var(--light-color) 0%, #fff 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .logo::before {
            content: "⚡";
            margin-right: 10px;
            font-size: 2rem;
        }
        
        .status {
            display: flex;
            align-items: center;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .status::before {
            content: "●";
            margin-right: 8px;
            color: #4CAF50;
        }
        
        main {
            padding: 4rem 0;
        }
        
        .hero {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }
        
        .hero p {
            font-size: 1.2rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto 2rem;
        }
        
        .services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }
        
        .service-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .service-card h3 {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .service-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #0AA876;
        }
        
        .btn-secondary {
            background: var(--secondary-color);
        }
        
        .btn-secondary:hover {
            background: #2E7BC6;
        }
        
        .info-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .info-section h2 {
            color: var(--dark-color);
            margin-bottom: 1rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✓";
            color: var(--primary-color);
            font-weight: bold;
            margin-right: 10px;
        }
        
        footer {
            background: var(--dark-color);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 4rem;
        }
        
        .footer-links {
            margin-bottom: 1rem;
        }
        
        .footer-links a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 1rem;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }
            
            .services {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    Matrix服务 - ${DOMAIN}
                </div>
                <div class="status">
                    服务正常运行
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <section class="hero">
                <h1>欢迎使用Matrix通信服务</h1>
                <p>基于开源Matrix协议的去中心化通信平台，提供安全、私密、可互操作的即时通讯服务</p>
            </section>

            <section class="services">
                <div class="service-card">
                    <h3>💬 Element Web客户端</h3>
                    <p>现代化的Web界面，支持文字聊天、语音通话、视频会议、文件分享等功能</p>
                    <a href="https://${ELEMENT_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}" class="btn" target="_blank">
                        打开Element Web
                    </a>
                </div>

                <div class="service-card">
                    <h3>🏠 Matrix服务器</h3>
                    <p>Synapse Matrix服务器，支持联邦通信，可与其他Matrix服务器互联互通</p>
                    <a href="https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}" class="btn btn-secondary" target="_blank">
                        访问服务器
                    </a>
                </div>

                <div class="service-card">
                    <h3>🔐 账户管理</h3>
                    <p>Matrix Authentication Service，提供用户注册、登录、密码管理等功能</p>
                    <a href="https://${MAS_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}" class="btn" target="_blank">
                        管理账户
                    </a>
                </div>

                <div class="service-card">
                    <h3>📞 音视频通话</h3>
                    <p>Matrix RTC服务，支持高质量的音视频通话和会议功能</p>
                    <a href="https://${MRTC_SUBDOMAIN}.${DOMAIN}:${CUSTOM_HTTPS_PORT}" class="btn btn-secondary" target="_blank">
                        音视频服务
                    </a>
                </div>
            </section>

            <section class="info-section">
                <h2>🚀 服务特性</h2>
                <ul class="feature-list">
                    <li>端到端加密通信，保护隐私安全</li>
                    <li>去中心化架构，无单点故障</li>
                    <li>支持联邦通信，与其他Matrix服务器互通</li>
                    <li>多平台客户端支持（Web、桌面、移动端）</li>
                    <li>丰富的功能：文字、语音、视频、文件分享</li>
                    <li>开源协议，透明可审计</li>
                    <li>自主部署，完全控制数据</li>
                </ul>
            </section>

            <section class="info-section">
                <h2>📱 客户端下载</h2>
                <p>除了Web版本，您还可以下载以下客户端：</p>
                <div style="margin-top: 1rem;">
                    <a href="https://element.io/get-started" class="btn" target="_blank" style="margin-right: 1rem;">Element桌面版</a>
                    <a href="https://element.io/get-started" class="btn btn-secondary" target="_blank">Element移动版</a>
                </div>
                <p style="margin-top: 1rem; color: #666;">
                    <strong>服务器地址：</strong> ${DOMAIN}<br>
                    Matrix客户端会自动发现服务器配置，只需输入主域名即可连接。
                </p>
            </section>

            <section class="info-section">
                <h2>🔧 技术信息</h2>
                <p>本服务基于以下技术构建：</p>
                <ul class="feature-list">
                    <li>Synapse Matrix服务器 (Python)</li>
                    <li>Element Web客户端 (React)</li>
                    <li>Matrix Authentication Service (Rust)</li>
                    <li>LiveKit音视频服务 (Go)</li>
                    <li>PostgreSQL数据库</li>
                    <li>Kubernetes容器编排</li>
                    <li>Let's Encrypt SSL证书</li>
                </ul>
            </section>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-links">
                <a href="https://matrix.org" target="_blank">Matrix.org</a>
                <a href="https://element.io" target="_blank">Element.io</a>
                <a href="https://github.com/matrix-org" target="_blank">GitHub</a>
            </div>
            <p>&copy; 2025 Matrix服务 - ${DOMAIN}. 基于开源Matrix协议构建.</p>
        </div>
    </footer>
</body>
</html>
