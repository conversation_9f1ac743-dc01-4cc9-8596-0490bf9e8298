#!/bin/bash
# Matrix部署脚本完整热修复
# 解决所有已知的函数和变量定义问题

set -e

echo "=== Matrix部署脚本完整热修复 ==="
echo "修复时间: $(date)"
echo

# 下载最新的setup.sh
echo "1. 下载最新的setup.sh..."
curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh -o setup.sh.original

# 创建备份
BACKUP_FILE="setup.sh.backup.$(date +%s)"
if [[ -f "setup.sh" ]]; then
    cp setup.sh "$BACKUP_FILE"
    echo "✓ 已备份现有文件: $BACKUP_FILE"
fi

cp setup.sh.original setup.sh
echo "✓ 已下载最新版本"

echo
echo "2. 应用热修复..."

# 修复1: 添加缺失的log_success函数
echo "修复1: 添加log_success函数定义..."
if ! grep -q "^log_success()" setup.sh; then
    # 在log_error函数后添加log_success函数
    sed -i '/^log_error() {/,/^}/ {
        /^}/a\
\
log_success() {\
    echo -e "${GREEN}[$(date '"'"'+%Y-%m-%d %H:%M:%S'"'"')] [SUCCESS]${NC} $*" | tee -a "${LOG_FILE:-/dev/null}"\
}
    }' setup.sh
    echo "✓ 已添加log_success函数"
else
    echo "✓ log_success函数已存在"
fi

# 修复2: 替换所有log_success调用为安全调用
echo "修复2: 修复log_success调用..."
sed -i 's/log_success "✓/echo "[SUCCESS] ✓/g' setup.sh
echo "✓ 已修复log_success调用"

# 修复3: 修正install_docker函数中的OS_TYPE问题
echo "修复3: 修正OS_TYPE变量问题..."
# 在install_docker函数开始处添加os_type检测
sed -i '/^install_docker() {/,/^}/ {
    /log_info "安装Docker..."/a\
    \
    # 检测操作系统类型\
    local os_type=""\
    if [[ -f /etc/os-release ]]; then\
        source /etc/os-release\
        os_type="$ID"\
    elif [[ -f /etc/debian_version ]]; then\
        os_type="debian"\
    elif [[ -f /etc/redhat-release ]]; then\
        os_type="centos"\
    else\
        os_type="unknown"\
    fi
}' setup.sh

# 替换所有OS_TYPE引用
sed -i 's/case "$OS_TYPE" in/case "$os_type" in/g' setup.sh
sed -i 's/\$OS_TYPE/\$os_type/g' setup.sh
echo "✓ 已修正OS_TYPE变量问题"

# 修复4: 修正systemctl检查
echo "修复4: 修正systemctl检查..."
sed -i 's/systemctl is-active --quiet k3s/systemctl is-active --quiet k3s 2>\/dev\/null/g' setup.sh
echo "✓ 已修正systemctl检查"

# 修复5: 确保所有log函数都有定义
echo "修复5: 确保所有log函数定义..."
# 检查并添加缺失的log函数
if ! grep -q "^log_warn()" setup.sh; then
    sed -i '/^log_error() {/,/^}/ {
        /^}/a\
\
log_warn() {\
    echo -e "${YELLOW}[$(date '"'"'+%Y-%m-%d %H:%M:%S'"'"')] [WARN]${NC} $*" | tee -a "${LOG_FILE:-/dev/null}"\
}
    }' setup.sh
    echo "✓ 已添加log_warn函数"
fi

# 修复6: 添加错误处理改进
echo "修复6: 改进错误处理..."
# 在install_docker函数末尾添加明确的成功日志
sed -i '/^install_docker() {/,/^}/ {
    /log_success "Docker安装完成"/c\
    echo "[SUCCESS] Docker安装完成"
}' setup.sh
echo "✓ 已改进错误处理"

# 修复7: 确保LOG_FILE变量定义
echo "修复7: 确保LOG_FILE变量定义..."
if ! grep -q "^LOG_FILE=" setup.sh; then
    sed -i '1a\
LOG_FILE="/tmp/matrix-deployment-$$.log"' setup.sh
    echo "✓ 已添加LOG_FILE变量定义"
fi

echo
echo "3. 验证修复结果..."

# 语法检查
if bash -n setup.sh; then
    echo "✓ 脚本语法检查通过"
else
    echo "✗ 脚本语法检查失败"
    if [[ -f "$BACKUP_FILE" ]]; then
        echo "恢复备份文件..."
        cp "$BACKUP_FILE" setup.sh
    fi
    exit 1
fi

# 检查关键函数
missing_functions=()
for func in log_info log_error log_warn log_success; do
    if grep -q "^${func}()" setup.sh; then
        echo "✓ $func 函数存在"
    else
        echo "⚠ $func 函数缺失"
        missing_functions+=("$func")
    fi
done

if [[ ${#missing_functions[@]} -eq 0 ]]; then
    echo "✓ 所有必要函数都已定义"
else
    echo "⚠ 缺失函数: ${missing_functions[*]}"
fi

# 设置执行权限
chmod +x setup.sh

echo
echo "=== 热修复完成 ==="
echo "修复后的文件: setup.sh"
echo "备份文件: $BACKUP_FILE"
echo "原始文件: setup.sh.original"
echo
echo "现在可以运行修复后的脚本："
echo "./setup.sh"
echo
echo "如果还有问题，可以查看备份文件或重新下载："
echo "cp $BACKUP_FILE setup.sh"
echo "或"
echo "curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh -o setup.sh"
