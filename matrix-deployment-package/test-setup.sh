#!/bin/bash
# Matrix部署脚本测试版本
# 用于验证基本功能和URL检测

set -euo pipefail

# === 环境检测函数 ===
detect_base_url() {
    # 优先使用环境变量
    if [[ -n "${DEPLOYMENT_BASE_URL:-}" ]]; then
        echo "$DEPLOYMENT_BASE_URL"
        return
    fi

    # 检查是否通过curl/wget执行（脚本路径包含/dev/fd/）
    local script_url="${BASH_SOURCE[0]}"
    if [[ "$script_url" =~ /dev/fd/ ]]; then
        # 通过curl执行，但无法确定源URL
        echo ""
        return
    fi

    # 本地执行，使用相对路径
    echo "$(dirname "$script_url")"
}

# === 全局变量定义 ===
SCRIPT_VERSION="1.0.0-test"
BASE_URL="$(detect_base_url)"
TEMP_DIR="/tmp/matrix-test-$$"

# 检查BASE_URL（测试脚本允许为空）
if [[ -z "$BASE_URL" ]]; then
    BASE_URL="[通过curl执行，无法检测源URL]"
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# === 日志函数 ===
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# === 清理函数 ===
cleanup() {
    if [[ -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
    fi
}

trap cleanup EXIT

# === 测试函数 ===
test_url_detection() {
    log_info "测试URL检测功能..."
    log_info "脚本路径: ${BASH_SOURCE[0]}"
    log_info "检测到的BASE_URL: $BASE_URL"
    
    # 测试是否能访问BASE_URL
    if curl -fsSL --connect-timeout 5 "$BASE_URL" >/dev/null 2>&1; then
        log_info "✓ BASE_URL可访问"
    else
        log_warn "⚠ BASE_URL不可访问，但这在测试中是正常的"
    fi
}

test_basic_functions() {
    log_info "测试基本功能..."
    
    # 创建临时目录
    mkdir -p "$TEMP_DIR"
    log_info "✓ 临时目录创建成功: $TEMP_DIR"
    
    # 测试环境检测
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_info "✓ 检测到Linux系统"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        log_info "✓ 检测到macOS系统"
    else
        log_warn "⚠ 未知系统类型: $OSTYPE"
    fi
    
    # 测试基本命令
    local commands=("curl" "wget" "git")
    for cmd in "${commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            log_info "✓ $cmd 可用"
        else
            log_warn "⚠ $cmd 不可用"
        fi
    done
}

show_config_info() {
    log_info "显示配置信息..."
    echo
    echo "=== 配置信息 ==="
    echo "脚本版本: $SCRIPT_VERSION"
    echo "BASE_URL: $BASE_URL"
    echo "临时目录: $TEMP_DIR"
    echo "系统类型: $OSTYPE"
    echo "架构: $(uname -m)"
    echo
}

# === 主函数 ===
main() {
    echo "=== Matrix部署脚本测试版本 ==="
    echo "版本: $SCRIPT_VERSION"
    echo
    
    test_url_detection
    echo
    test_basic_functions
    echo
    show_config_info
    
    log_info "✓ 测试完成！"
    
    if [[ "${1:-}" == "--interactive" ]]; then
        echo
        read -p "是否继续运行完整部署脚本？[y/N]: " continue_deploy
        if [[ "$continue_deploy" =~ ^[Yy]$ ]]; then
            log_info "启动完整部署脚本..."
            # 这里可以调用完整的setup.sh
            exec bash <(curl -fsSL "${BASE_URL}/setup.sh")
        fi
    fi
}

# 执行主函数
main "$@"
