# Matrix部署包部署指南

## 🚀 快速部署

### 方法1：一键安装（推荐）

```bash
# 设置部署源（必须）- 替换为您的实际仓库地址
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/用户名/仓库名/分支名"

# 执行安装
bash <(curl -fsSL https://raw.githubusercontent.com/用户名/仓库名/分支名/setup.sh)
```

**示例**：
```bash
# GitHub仓库示例
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/myuser/matrix-deploy/main"
bash <(curl -fsSL https://raw.githubusercontent.com/myuser/matrix-deploy/main/setup.sh)

# GitLab仓库示例
export DEPLOYMENT_BASE_URL="https://gitlab.com/myuser/matrix-deploy/-/raw/main"
bash <(curl -fsSL https://gitlab.com/myuser/matrix-deploy/-/raw/main/setup.sh)

# 自定义服务器示例
export DEPLOYMENT_BASE_URL="https://files.example.com/matrix"
bash <(curl -fsSL https://files.example.com/matrix/setup.sh)
```

### 方法2：本地部署

```bash
# 下载部署包（替换为您的仓库地址）
git clone https://github.com/用户名/仓库名.git
cd 仓库名

# 执行安装
./setup.sh
```

### 方法3：测试版本

```bash
# 测试基本功能（替换为您的仓库地址）
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/用户名/仓库名/分支名"
bash <(curl -fsSL https://raw.githubusercontent.com/用户名/仓库名/分支名/test-setup.sh)
```

## 🔧 故障排除

### 问题1：`detect_base_url: command not found`

**原因**：函数定义顺序问题

**解决方案**：
1. 确保使用最新版本的setup.sh
2. 设置环境变量：`export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/用户名/仓库名/分支名"`

### 问题2：`curl: (23) Failure writing output to destination`

**原因**：网络连接或权限问题

**解决方案**：
1. 检查网络连接
2. 使用wget替代：`bash <(wget -qO- https://raw.githubusercontent.com/用户名/仓库名/分支名/setup.sh)`
3. 本地下载后执行

### 问题3：模板文件下载失败

**原因**：BASE_URL检测错误或文件不存在

**解决方案**：
1. 手动设置BASE_URL：`export DEPLOYMENT_BASE_URL="正确的URL"`
2. 检查GitHub仓库中文件是否存在
3. 使用本地部署方法

## 📁 文件结构要求

确保GitHub仓库包含以下文件：

```
repository/
├── setup.sh                    # 主安装脚本
├── test-setup.sh              # 测试脚本
├── templates/                  # 配置模板
│   ├── values.yaml.template
│   ├── nginx.conf.template
│   └── index.html.template
└── scripts/                    # 辅助脚本
    ├── routeros-api.py
    ├── virtual-ip-manager.sh
    └── validate-config.sh
```

## 🔍 调试模式

### 启用详细日志

```bash
# 设置调试模式
export DEBUG=1
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/niublab/test/main"

# 执行安装
bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)
```

### 查看日志

```bash
# 查看安装日志
tail -f /tmp/matrix-deployment-*.log

# 查看系统日志
journalctl -f
```

## 🌐 支持的部署源

### GitHub（推荐）
```bash
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/用户名/仓库名/分支名"
```

### GitLab
```bash
export DEPLOYMENT_BASE_URL="https://gitlab.com/用户名/仓库名/-/raw/分支名"
```

### 自定义服务器
```bash
export DEPLOYMENT_BASE_URL="https://your-domain.com/path/to/files"
```

## ⚙️ 环境要求

### 内部服务器
- **操作系统**：Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **内存**：最少2GB，推荐4GB+
- **CPU**：最少2核，推荐4核+
- **存储**：最少20GB可用空间
- **网络**：需要访问互联网下载依赖

### 外部服务器
- **操作系统**：任何支持Nginx的Linux发行版
- **内存**：最少512MB
- **CPU**：最少1核
- **存储**：最少1GB可用空间
- **网络**：需要公网IP和域名

## 🔐 安全注意事项

1. **RouterOS密码**：安装过程中输入的RouterOS密码会保存在配置文件中，请确保文件权限正确
2. **Cloudflare API Token**：请使用最小权限的API Token
3. **SSL证书**：建议使用Let's Encrypt自动证书
4. **防火墙**：脚本会自动配置防火墙规则，请确认规则符合安全要求

## 📞 技术支持

如果遇到问题，请提供以下信息：

1. **错误信息**：完整的错误输出
2. **系统信息**：`uname -a` 和 `cat /etc/os-release`
3. **网络环境**：是否使用代理、防火墙配置等
4. **部署日志**：`/tmp/matrix-deployment-*.log` 文件内容

## 🔄 更新部署包

```bash
# 重新下载最新版本
export DEPLOYMENT_BASE_URL="https://raw.githubusercontent.com/niublab/test/main"
bash <(curl -fsSL https://raw.githubusercontent.com/niublab/test/main/setup.sh)
```

---

**版本**：v1.0.0  
**更新时间**：2025-06-21  
**维护者**：Matrix混合部署项目团队
