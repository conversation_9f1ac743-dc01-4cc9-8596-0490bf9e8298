#!/bin/bash
# 测试URL构建修复

set -e

echo "=== 测试URL构建修复 ==="
echo "时间: $(date)"
echo

# 导入颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 测试detect_base_url函数
test_url_detection() {
    echo "1. 测试URL检测和修正..."
    
    # 模拟不同的URL格式
    local test_urls=(
        "https://raw.githubusercontent.com/niublab/test/main/setup.sh"
        "https://raw.githubusercontent.com/user/repo/master/setup.sh"
        "https://raw.githubusercontent.com/org/project/main/setup.sh"
    )
    
    for url in "${test_urls[@]}"; do
        echo "测试URL: $url"
        
        # 提取base URL
        local base_url="$(dirname "$url")"
        echo "  原始base URL: $base_url"
        
        # 修正GitHub URL格式
        if [[ "$base_url" =~ raw\.githubusercontent\.com.*/(main|master)$ ]]; then
            # 提取用户名和仓库名
            local repo_path=$(echo "$base_url" | sed -n 's|.*raw\.githubusercontent\.com/\([^/]*/[^/]*\)/\(main\|master\)|\1|p')
            local branch=$(echo "$base_url" | sed -n 's|.*raw\.githubusercontent\.com/[^/]*/[^/]*/\(main\|master\)|\1|p')
            
            if [[ -n "$repo_path" && -n "$branch" ]]; then
                base_url="https://raw.githubusercontent.com/${repo_path}/refs/heads/${branch}"
                echo "  修正后base URL: $base_url"
                echo "  ✓ URL修正成功"
            else
                echo "  ✗ URL修正失败"
            fi
        else
            echo "  - 不需要修正"
        fi
        echo
    done
}

# 测试脚本下载URL构建
test_script_download_urls() {
    echo "2. 测试脚本下载URL构建..."
    
    local base_url="https://raw.githubusercontent.com/niublab/test/refs/heads/main"
    local scripts=("routeros-api.py" "virtual-ip-manager.sh")
    
    echo "Base URL: $base_url"
    echo
    
    for script in "${scripts[@]}"; do
        local script_url="${base_url}/scripts/${script}"
        echo "脚本: $script"
        echo "  下载URL: $script_url"
        
        # 测试URL是否可访问（仅检查HTTP状态码）
        if curl -s --head "$script_url" | head -n 1 | grep -q "200 OK"; then
            echo "  ✓ URL可访问"
        else
            echo "  ⚠ URL不可访问（可能是网络问题或文件不存在）"
        fi
        echo
    done
}

# 测试模板下载URL构建
test_template_download_urls() {
    echo "3. 测试模板下载URL构建..."
    
    local base_url="https://raw.githubusercontent.com/niublab/test/refs/heads/main"
    local templates=("values.yaml" "nginx.conf" "index.html")
    
    echo "Base URL: $base_url"
    echo
    
    for template in "${templates[@]}"; do
        local template_url="${base_url}/templates/${template}.template"
        echo "模板: $template"
        echo "  下载URL: $template_url"
        
        # 测试URL是否可访问（仅检查HTTP状态码）
        if curl -s --head "$template_url" | head -n 1 | grep -q "200 OK"; then
            echo "  ✓ URL可访问"
        else
            echo "  ⚠ URL不可访问（可能是网络问题或文件不存在）"
        fi
        echo
    done
}

# 测试自包含模式
test_self_contained_mode() {
    echo "4. 测试自包含模式..."
    
    # 检查内置脚本生成函数是否存在
    if grep -q "generate_builtin_script" setup.sh; then
        echo "✓ generate_builtin_script函数存在"
    else
        echo "✗ generate_builtin_script函数缺失"
        return 1
    fi
    
    if grep -q "generate_builtin_routeros_script" setup.sh; then
        echo "✓ generate_builtin_routeros_script函数存在"
    else
        echo "✗ generate_builtin_routeros_script函数缺失"
        return 1
    fi
    
    if grep -q "generate_builtin_virtual_ip_script" setup.sh; then
        echo "✓ generate_builtin_virtual_ip_script函数存在"
    else
        echo "✗ generate_builtin_virtual_ip_script函数缺失"
        return 1
    fi
    
    echo "✓ 自包含模式支持完整"
}

# 测试错误处理改进
test_error_handling() {
    echo "5. 测试错误处理改进..."
    
    # 检查download_script函数是否包含错误处理
    if grep -A 10 "download_script()" setup.sh | grep -q "log_warn.*远程下载失败"; then
        echo "✓ download_script包含错误处理"
    else
        echo "✗ download_script缺少错误处理"
        return 1
    fi
    
    if grep -A 10 "download_template()" setup.sh | grep -q "log_warn.*远程下载失败"; then
        echo "✓ download_template包含错误处理"
    else
        echo "✗ download_template缺少错误处理"
        return 1
    fi
    
    echo "✓ 错误处理改进完整"
}

# 运行所有测试
echo "开始测试..."
echo

test_url_detection
test_script_download_urls
test_template_download_urls
test_self_contained_mode
echo
test_error_handling
echo

echo "=== 测试完成 ==="
echo
echo "✅ URL构建修复验证完成！"
echo
echo "主要修复："
echo "1. ✅ GitHub URL格式修正：/main/ -> /refs/heads/main/"
echo "2. ✅ 智能URL检测和修正逻辑"
echo "3. ✅ 自包含模式支持（内置脚本生成）"
echo "4. ✅ 改进的错误处理和降级策略"
echo "5. ✅ 详细的下载日志和调试信息"
echo
echo "现在脚本应该能够："
echo "- 正确构建GitHub raw文件URL"
echo "- 在远程下载失败时使用内置脚本"
echo "- 提供详细的错误信息和调试日志"
echo "- 支持多种Git仓库和分支结构"
